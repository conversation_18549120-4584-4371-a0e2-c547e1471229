# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Core Principles
1. **Simplicity First**: Make minimal changes with maximum impact - avoid complex modifications
2. **Cache-First Strategy**: Always check ServerCache before database operations
3. **Security Always**: Validate authId, use user-scoped queries, verify file paths
4. **Performance Optimized**: Mobile-first design, local state mutations for instant UI updates
5. **Documentation**: For detailed patterns, reference docs/ files as needed

## Workflow Rules
1. Think through the problem, read relevant files, write plan to tasks/todo.md
2. Create todo items that can be checked off as completed
3. Check in with user before starting work to verify the plan
4. Work on todo items, marking them complete as you go
5. Provide high-level explanations of changes made
6. Add review section to todo.md with summary of changes

## Tech Stack
- **Framework**: Next.js 14 App Router + TypeScript (strict mode)
- **Database**: PostgreSQL via N8N webhooks + Supabase Auth
- **Caching**: Upstash Redis + ServerCache (in-memory, 30min TTL)
- **Storage**: Cloudflare R2 + Cloudflare Images optimization
- **Styling**: Tailwind CSS + Framer Motion
- **Paths**: `@/*` → `./src/*`

## Development Commands
```bash
npm run dev          # Start development server (auto-kills port 3000)
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
```

## Project Structure
```
src/
├── app/
│   ├── api/           # API routes by domain
│   │   ├── auth/      # Authentication endpoints
│   │   ├── dashboard/ # Dashboard data APIs
│   │   ├── knowledge/ # Knowledge base management
│   │   └── user/      # User data (cached endpoints)
│   └── dashboard/     # Dashboard pages
├── components/        # Reusable components
│   └── ui/           # UI components + modals + forms
├── context/          # React Context providers
├── hooks/            # Custom React hooks
├── lib/              # Core libraries (cache, analytics)
└── utils/            # Utility functions
```

## Essential Patterns

### Caching Strategy
- **3-Tier System**: ServerCache (30min TTL) → Hook Cache → Component State
- **Cache Keys**: `dashboard_${authId}` and `knowledge_${authId}`
- **Pattern**: Check cache first, fallback to database via webhook, update cache
- **API**: `/api/user/data?cache=dashboard|knowledge`
- **Details**: See `docs/development/caching-strategy.md`

### Authentication Flow
```typescript
// Server-side auth verification
const { authenticated, authId } = await verifyAuth()

// Client-side auth context
const { user, session } = useAuth()
```

### Data Flow Pattern
```
UI Components → Next.js API Routes → Webhook Endpoints → PostgreSQL Database
```

### Local State Mutations
```typescript
// Instant UI updates without server refetch
const { addPhotoToState, updatePhotoInState, removePhotoFromState } = useKnowledgeData()
```

## Code Style Guidelines
- **Imports**: Use `@/` for src/ imports
- **Components**: Prefer function components with React hooks
- **TypeScript**: Use strict mode, avoid `any` type
- **Error Handling**: Always include error handling and loading states
- **Security**: Validate authId on all API routes, user-scoped queries only
- **Performance**: Use React.memo, useMemo, useCallback for optimization

## API Development Pattern
```typescript
// Standard API endpoint structure
const { authenticated, authId } = await verifyAuth()          // 1. Auth check
const cacheKey = `cache_${authId}`                           // 2. Cache key
const cachedData = serverCache.get(cacheKey)                // 3. Check cache
if (cachedData) return cachedData                           // 4. Return if cached
const freshData = await fetchFromWebhook()                 // 5. Database query
serverCache.set(cacheKey, freshData, 30)                   // 6. Update cache
return freshData                                            // 7. Return response
```

## File Operations Pattern
```typescript
// Upload: Client compress → R2 upload → Database save → Local state update
// Update: Keep existing + upload new → Delete removed → Update DB → Update state
// Delete: Check dependencies → Delete from DB + R2 → Update local state
```

## Documentation References

When working on specific features, reference these detailed documentation files:

### Development Patterns
- **Caching Strategy**: `docs/development/caching-strategy.md`
- **API Development**: `docs/development/api-patterns.md`  
- **Authentication**: `docs/development/authentication.md`

### Feature Implementation
- **Photo Management**: `docs/features/photo-management.md`
- **Knowledge System**: `docs/features/knowledge-system.md`
- **Knowledge Base Page**: `docs/features/knowledge-base-page.md`
- **Intro Management**: `docs/features/intro-management.md`
- **Connect Platform Management**: `docs/features/connect-platform-management.md`


## Key Constraints
- Never edit files without understanding their dependencies
- Always test caching behavior with `npm run start` (production mode)
- Validate all user inputs and file paths for security
- Use local state mutations for instant UI feedback
- Follow the established patterns for consistency

## Getting Detailed Help
When you need specific implementation details, ask me to read the relevant docs/ file:
- "Read docs/development/caching-strategy.md for caching patterns"
- "Check docs/features/photo-management.md for photo workflows" 
- "See docs/architecture/webhook-integration.md for webhook patterns"