# API Development Patterns

This document outlines the standard patterns and conventions used for API development in the chhlat-bot project.

## Standard API Endpoint Structure

### Basic Pattern
```typescript
// Standard API endpoint structure
export async function POST(request: Request) {
  try {
    // 1. Auth verification with client identification
    const { authenticated, authId, clientId } = await verifyAuth()
    if (!authenticated || !authId) {
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Unauthorized'
      }, { status: 401 })
    }

    // 2. Validate client ID if required
    if (!clientId) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Client ID not found in authentication'
      }, { status: 400 })
    }

    // 3. Cache check (if applicable)
    const cacheKey = `cache_${authId}`
    const cachedData = serverCache.get(cacheKey)
    if (cachedData) {
      return NextResponse.json({
        success: true,
        body: cachedData,
        cached: true
      })
    }

    // 4. Request validation
    const { data } = await request.json()
    if (!data) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Missing required data'
      }, { status: 400 })
    }

    // 5. Database operation via webhook using clientId directly
    const freshData = await fetchFromWebhook(data, clientId)
    
    // 6. Cache update (if applicable)
    serverCache.set(cacheKey, freshData, 30)
    
    // 7. Return response
    return NextResponse.json({
      success: true,
      body: freshData,
      error_msg: null
    })

  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json(
      { 
        success: false,
        body: null,
        error_msg: error instanceof Error ? error.message : 'Internal server error'
      },
      { status: 500 }
    )
  }
}
```

## Authentication Pattern

### Server-Side Auth Verification
```typescript
// Import auth utility
import { verifyAuth } from '@/utils/auth'

// Use in all API endpoints with client identification
const { authenticated, authId, clientId } = await verifyAuth()
if (!authenticated || !authId) {
  return NextResponse.json({ 
    success: false,
    body: null,
    error_msg: 'Unauthorized'
  }, { status: 401 })
}

// Validate client ID if required for the operation
if (!clientId) {
  return NextResponse.json({
    success: false,
    body: null,
    error_msg: 'Client ID not found in authentication'
  }, { status: 400 })
}
```

### Client-Side Auth Context
```typescript
// Use auth context in components
import { useAuth } from '@/context/AuthContext'

const { user, session, signIn, signOut } = useAuth()
```

## Cache-First API Pattern

### Unified Cache Endpoint
```typescript
// /api/user/data/route.ts - Unified cache endpoint
export async function GET(request: NextRequest) {
  try {
    const { authenticated, authId } = await verifyAuth()
    if (!authenticated) return unauthorizedResponse()

    const { searchParams } = new URL(request.url)
    const cacheType = searchParams.get('cache') || 'dashboard'
    
    if (!['dashboard', 'knowledge'].includes(cacheType)) {
      return badRequestResponse('Invalid cache type')
    }

    const cacheKey = `${cacheType}_${authId}`
    const cachedData = serverCache.get(cacheKey)
    
    if (cachedData) {
      return NextResponse.json({
        success: true,
        body: cachedData,
        cached: true
      })
    }

    // Fetch fresh data from database
    const freshData = await fetchFromDatabase(cacheType, authId)
    
    // Cache for 30 minutes
    serverCache.set(cacheKey, freshData, 30)
    
    return NextResponse.json({
      success: true,
      body: freshData,
      cached: false
    })

  } catch (error) {
    return errorResponse(error)
  }
}
```

### Cache Update Pattern
```typescript
// Always update cache after mutations
const updateCache = (cacheKey: string, updatedData: any) => {
  const existingCache = serverCache.get(cacheKey)
  if (existingCache) {
    // Update existing cache only
    serverCache.set(cacheKey, updatedData, 30)
  }
  // Don't create new cache entries
}
```

## Webhook Integration Pattern

### JWT Token Generation
```typescript
// Generate JWT token for webhook authentication
import { generateWebhookToken } from '@/utils/jwt'

const jwtToken = generateWebhookToken()
```

### Webhook Request Format
```typescript
// Standard webhook request
const response = await fetch(webhookUrl, {
  method: 'POST',
  headers: { 
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${jwtToken}`
  },
  body: JSON.stringify({
    operation: 'SELECT|INSERT|UPDATE|DELETE',
    sql: 'SQL query string',
    params: [param1, param2],
    // Optional context fields (for webhook logic, not database)
    mode?: 'welcome|knowledge|dashboard',
    sector?: string | null,
    lang?: string | null,
    is_audio?: boolean,
    onlyPhoto?: boolean
  })
})

// Handle webhook response
if (!response.ok) {
  throw new Error(`Webhook request failed: ${response.status}`)
}

let webhookData = await response.json()

// Handle array response format from N8N webhook
if (Array.isArray(webhookData) && webhookData.length > 0) {
  webhookData = webhookData[0]
}

if (!webhookData.success) {
  throw new Error(webhookData.error_msg || 'Database operation failed')
}
```

## Webhook Response Handling

### N8N Webhook Array Wrapper Pattern
All N8N webhook responses come wrapped in an array format that must be extracted:

```typescript
// N8N webhook response structure: [{success, body, error_msg}]
let webhookData = await response.json()

// Extract first element from N8N array wrapper
if (Array.isArray(webhookData) && webhookData.length > 0) {
  webhookData = webhookData[0]
}

// Now access standard response properties
if (!webhookData.success) {
  throw new Error(webhookData.error_msg || 'Database operation failed')
}

// Extract data from body (body is also always an array for database results)
const result = webhookData.body?.[0] // Single result extraction
// OR
const results = webhookData.body // Multiple results (keep as array)
```

### Frontend API Response Handling
Frontend code should also handle the array wrapper when calling APIs that use webhooks:

```typescript
// Frontend API call pattern
const response = await fetch('/api/knowledge/welcome-chat')
let responseData = await response.json()

// Handle array response format from N8N webhook (via API)
if (Array.isArray(responseData) && responseData.length > 0) {
  responseData = responseData[0]
}

if (!responseData.success) {
  console.error('Error:', responseData.error_msg)
  return
}

// Use responseData.body
const data = responseData.body
```

## Response Patterns

### Standard Response Structure
```typescript
// Success response
{
  success: true,
  body: data,
  error_msg: null,
  cached?: boolean // Optional cache indicator
}

// Error response
{
  success: false,
  body: null,
  error_msg: string
}
```

### Response Helpers
```typescript
// Helper functions for consistent responses
const successResponse = (data: any, cached = false) => {
  return NextResponse.json({
    success: true,
    body: data,
    error_msg: null,
    ...(cached && { cached: true })
  })
}

const errorResponse = (error: Error | string, status = 500) => {
  return NextResponse.json({
    success: false,
    body: null,
    error_msg: error instanceof Error ? error.message : error
  }, { status })
}

const unauthorizedResponse = () => {
  return NextResponse.json({
    success: false,
    body: null,
    error_msg: 'Unauthorized'
  }, { status: 401 })
}

const badRequestResponse = (message: string) => {
  return NextResponse.json({
    success: false,
    body: null,
    error_msg: message
  }, { status: 400 })
}
```

## File Upload Pattern

### R2 File Upload
```typescript
// /api/file/photos/route.ts
export async function POST(request: Request) {
  try {
    const { authenticated, authId } = await verifyAuth()
    if (!authenticated) return unauthorizedResponse()

    const formData = await request.formData()
    const files = formData.getAll('files') as File[]
    const photoId = formData.get('photoId') as string

    // Validate files
    for (const file of files) {
      const validation = validateFile(file)
      if (!validation.valid) {
        return badRequestResponse(validation.error)
      }
    }

    // Upload to R2
    const uploadResults = await uploadToR2(files, `photos/${authId}/${photoId}`)
    
    return successResponse({
      photoUrls: uploadResults.urls,
      filePaths: uploadResults.paths
    })

  } catch (error) {
    return errorResponse(error)
  }
}
```

### File Validation
```typescript
// File validation helper
const validateFile = (file: File) => {
  const validTypes = ['image/jpeg', 'image/jpg', 'image/png']
  const maxSize = 5 * 1024 * 1024 // 5MB

  if (!validTypes.includes(file.type)) {
    return { valid: false, error: 'Only JPEG and PNG files are allowed' }
  }

  if (file.size > maxSize) {
    return { valid: false, error: 'File size must be less than 5MB' }
  }

  return { valid: true }
}
```

## Security Patterns

### Path Validation
```typescript
// Validate file paths belong to authenticated user
const validateUserPath = (filePath: string, authId: string) => {
  const userPrefix = `photos/${authId}/`
  return filePath.startsWith(userPrefix)
}

// Usage in API
const filePaths = ['path1', 'path2']
const invalidPaths = filePaths.filter(path => !validateUserPath(path, authId))

if (invalidPaths.length > 0) {
  return badRequestResponse('Invalid file paths detected')
}
```

### User-Scoped Queries
```typescript
// Always include user scoping in database queries using clientId from auth
const { authenticated, authId, clientId } = await verifyAuth()

const sql = `
  SELECT * FROM photos 
  WHERE client_id = $1 AND photo_id = $2
`
const params = [clientId, photoId] // clientId from verifyAuth(), not cache

// Never allow unscoped queries
// BAD: SELECT * FROM photos WHERE photo_id = $1
// GOOD: SELECT * FROM photos WHERE client_id = $1 AND photo_id = $2
// BEST: Use clientId from verifyAuth() for optimal performance
```

## Batch Processing Pattern

### FAQ Batch Processing
```typescript
// /api/knowledge/add-batch/route.ts
export async function POST(request: Request) {
  try {
    const { authenticated, authId, clientId } = await verifyAuth()
    if (!authenticated || !clientId) return unauthorizedResponse()

    const { faqBatch } = await request.json()
    
    // Validate batch size
    const MAX_BATCH_SIZE = 20
    if (faqBatch.length > MAX_BATCH_SIZE) {
      return badRequestResponse(`Batch size exceeds limit of ${MAX_BATCH_SIZE}`)
    }

    // Process items in chunks using clientId from auth
    const chunkSize = 5
    const chunks = chunkArray(faqBatch, chunkSize)
    const results = []

    for (const chunk of chunks) {
      const chunkResults = await Promise.all(
        chunk.map(item => processItem(item, clientId)) // Use clientId from auth
      )
      results.push(...chunkResults)
    }

    const successful = results.filter(r => r.success)
    const failed = results.filter(r => !r.success)

    return successResponse({
      items_processed: successful.length,
      items_failed: failed.length,
      failures: failed.map(f => ({ item: f.item, error: f.error }))
    })

  } catch (error) {
    return errorResponse(error)
  }
}
```

## Error Handling Patterns

### Comprehensive Error Handling
```typescript
try {
  // API logic here
} catch (error: unknown) {
  console.error('API Error:', error)
  
  // Handle different error types
  if (error instanceof ValidationError) {
    return badRequestResponse(error.message)
  }
  
  if (error instanceof AuthError) {
    return unauthorizedResponse()
  }
  
  if (error instanceof DatabaseError) {
    return errorResponse('Database operation failed', 500)
  }
  
  // Generic error fallback
  return errorResponse(
    error instanceof Error ? error.message : 'Internal server error'
  )
}
```

### Input Validation
```typescript
// Validate required fields
const validateRequiredFields = (data: any, fields: string[]) => {
  const missing = fields.filter(field => !data[field])
  if (missing.length > 0) {
    throw new ValidationError(`Missing required fields: ${missing.join(', ')}`)
  }
}

// Usage
validateRequiredFields(requestData, ['photo_id', 'photo_url'])
```

## Performance Patterns

### Request Deduplication
```typescript
// Global request promises to prevent duplicate calls
let activeRequests = new Map<string, Promise<any>>()

const deduplicateRequest = async (key: string, requestFn: () => Promise<any>) => {
  if (activeRequests.has(key)) {
    return await activeRequests.get(key)
  }
  
  const promise = requestFn()
  activeRequests.set(key, promise)
  
  try {
    const result = await promise
    return result
  } finally {
    activeRequests.delete(key)
  }
}
```

### Database Connection Pooling
```typescript
// Use connection pooling for database operations
const getWebhookUrl = () => {
  return process.env.CHHLAT_DB_WEBHOOK_URL
}

const executeQuery = async (sql: string, params: any[]) => {
  const webhookUrl = getWebhookUrl()
  if (!webhookUrl) {
    throw new Error('Database webhook not configured')
  }
  
  // Use pooled connection via webhook
  return await fetch(webhookUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${jwtToken}`
    },
    body: JSON.stringify({
      operation: 'SELECT',
      sql,
      params
    })
  })
}
```

## Best Practices

1. **Always authenticate** before processing requests
2. **Use cache-first strategy** for frequently accessed data
3. **Validate all inputs** before processing
4. **Use consistent response format** across all endpoints
5. **Handle errors gracefully** with appropriate HTTP status codes
6. **Log errors** for debugging and monitoring
7. **Use TypeScript** for better type safety
8. **Implement request deduplication** for performance
9. **Use user-scoped queries** for security
10. **Test with both success and error scenarios**

## Common Patterns by Feature

### Dashboard APIs
- Use `/api/user/data?cache=dashboard` for cached data
- Include usage statistics and client info
- Implement request deduplication

### Knowledge APIs
- Use `/api/user/data?cache=knowledge` for cached data
- Include photos array and knowledge stats
- Support batch operations for FAQs

### File APIs
- Always validate file types and sizes
- Use user-scoped path validation
- Support batch uploads and deletions

### Authentication APIs
- Use secure session management
- Implement proper logout cleanup
- Handle auth errors gracefully