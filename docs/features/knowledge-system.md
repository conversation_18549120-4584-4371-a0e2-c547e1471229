# Knowledge Management System

Handles FAQ creation, audio processing, and photo attachment workflows with batch processing capabilities.

## System Overview

### Core Features
- **FAQ Creation**: Text and audio answers with photo attachments
- **Batch Processing**: Up to 20 FAQs per batch with audio validation
- **Audio Pipeline**: Redis validation → Telegram download → R2 upload
- **Photo Integration**: Search and attach photos from knowledge cache
- **Real-time Updates**: Optimistic UI with local state mutations

### Architecture
- **Data Source**: Unified knowledge cache via `useKnowledgeData()` hook
- **Cache Strategy**: 30-minute server cache with `knowledge_${authId}` key
- **API Endpoint**: `/api/user/data?cache=knowledge`
- **State Management**: Local batch state with optimistic updates

## FAQ Management

### Batch Processing Flow
1. **Local Creation**: Add FAQs to local state (max 20 per batch)
2. **Validation**: Validate questions, answers, and audio codes
3. **API Processing**: Send batch to `/api/knowledge/add-batch`
4. **Audio Processing**: Process audio FAQs in chunks of 5
5. **State Updates**: Update local FAQ count optimistically

### Validation Rules
- **Questions**: Required, non-empty strings
- **Text Answers**: Required for non-audio FAQs
- **Audio Codes**: Required for audio FAQs, validated via Redis
- **Batch Size**: Maximum 20 FAQs per batch

### Error Handling
- Individual FAQ validation with specific error messages
- Batch processing with partial failure support
- Audio processing errors handled gracefully

## Audio Processing

### Audio Pipeline
1. **Validation**: Redis-based audio code validation via `/api/audio/validate`
2. **Download**: Telegram audio file download using file_id
3. **Conversion**: MP4A AAC format conversion with validation
4. **Upload**: R2 storage upload via `/api/file/audios`
5. **Processing**: Chunked processing (5 FAQs per chunk) for performance

### Audio Code Validation
- **Source**: Upstash Redis storage
- **Returns**: Duration and file_id for valid codes
- **Reusability**: Codes can be reused (not marked as consumed)
- **Debouncing**: 500ms delay for real-time validation

### Performance Optimizations
- **Chunked Processing**: Audio FAQs processed in groups of 5
- **Parallel Processing**: Multiple audio files processed simultaneously
- **Error Isolation**: Individual FAQ failures don't affect batch
- **Timeout Prevention**: Chunking prevents API timeouts

## Photo Management

### Search Strategy
- **Primary**: Local search in cached photos from knowledge cache
- **Fallback**: API search via `/api/knowledge/search-photos` if cache miss
- **Performance**: PostgreSQL ILIKE search with 20 result limit
- **Scope**: User-scoped search (only own photos)

### Photo Attachment
- **Selection**: Store photo_id, thumbnail URL, and full URL array
- **Display**: Thumbnail for UI, full array for gallery modal
- **Integration**: Attach to FAQs during batch processing
- **Clearing**: Reset selection state and search results

## Real-time Updates

### Optimistic UI Pattern
- **Photo Operations**: `addPhotoToState()`, `updatePhotoInState()`, `removePhotoFromState()`
- **FAQ Count**: Update count and usage percentage immediately
- **Background Sync**: Server synchronization happens asynchronously
- **Rollback**: Error handling can revert optimistic changes

### State Management
- **Local State**: Immediate UI updates for better UX
- **Cache Updates**: Update knowledge cache after successful operations
- **Consistency**: Ensure UI state matches server state eventually

## API Endpoints

### POST `/api/knowledge/add-batch` - FAQ Batch Processing
**Purpose**: Process up to 20 FAQs per batch with audio and photo support
**Features**:
- Mixed text and audio FAQ processing
- Photo attachment support
- Partial failure handling
- Knowledge cache updates

### POST `/api/audio/validate` - Audio Code Validation
**Purpose**: Validate audio codes against Redis storage
**Returns**: Duration and file_id for valid codes
**Features**:
- Real-time validation with debouncing
- Audio code reusability
- Telegram file_id retrieval

### POST `/api/knowledge/search-photos` - Photo Search
**Purpose**: Search photos by photo_id with PostgreSQL ILIKE
**Features**:
- User-scoped search (own photos only)
- 20 result limit for performance
- Fallback for cache misses

### Common Patterns
- **N8N Response Handling**: Extract from array wrapper format
- **Authentication**: All endpoints require user authentication
- **Error Handling**: Comprehensive error messages and status codes

## Performance Strategy

### Caching Layers
1. **Server Cache**: 30-minute TTL for database queries
2. **Hook Cache**: Request deduplication and local state
3. **Component State**: Instant UI updates with optimistic rendering

### Processing Optimizations
- **Audio Chunking**: Process 5 audio FAQs per chunk to prevent timeouts
- **Local Search**: Search cached photos before API calls
- **Request Deduplication**: Prevent duplicate concurrent requests
- **Batch Processing**: Handle up to 20 FAQs per batch efficiently

### UI Performance
- **Optimistic Updates**: Immediate UI feedback without server round-trips
- **Debounced Validation**: 500ms delay for audio code validation
- **Lazy Loading**: Load photos only when needed

## Implementation Status

### Core Features ✅
- FAQ batch processing (up to 20 per batch)
- Audio validation and processing pipeline
- Photo search and attachment system
- Real-time UI updates with optimistic rendering
- 3-tier caching strategy implementation

### Integration Points
- **Authentication**: User-scoped operations with `authId`
- **Caching**: Knowledge cache updates after mutations
- **Storage**: R2 for audio files, photo references by ID
- **Database**: Webhook integration with batch operations

### Key Patterns
1. Validate all inputs before processing
2. Use batch processing for multiple FAQs
3. Handle audio processing errors gracefully
4. Cache photo search results locally
5. Use optimistic UI updates for better UX
6. Chunk audio processing to prevent timeouts
7. Update cache after mutations for consistency