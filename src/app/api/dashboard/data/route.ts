import { NextResponse } from 'next/server'
import { verifyAuth } from '@/utils/auth'
import { generateWebhookToken } from '@/utils/jwt'
import { serverCache } from '@/lib/cache'

export async function GET() {
  try {
    // Verify authentication
    const { authenticated, authId } = await verifyAuth()
    if (!authenticated || !authId) {
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Unauthorized'
      }, { status: 401 })
    }

    // Note: We'll check cache after getting client_id from database
    let cacheKey: string
    let cachedResult: any

    // Get the secure webhook URL from environment
    const webhookUrl = process.env.CHHLAT_DB_WEBHOOK_URL
    
    if (!webhookUrl) {
      console.warn('Secure webhook URL not configured')
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Database webhook not configured'
      }, { status: 500 })
    }

    // Generate JWT token for webhook authentication
    const jwtToken = generateWebhookToken()

    // Prepare SQL query for dashboard data
    const sql = `
      SELECT 
        c.client_id,
        c.username,
        c.sector,
        c.lang,
        c.plan_type,
        c.next_billing_date,
        c.usage_used,
        c.usage_limit,
        COALESCE(p.total_faqs, 0) as faq_limit,
        COALESCE(p.total_photos, 0) as photo_limit,
        (SELECT COUNT(*) FROM faqs WHERE client_id = c.client_id AND is_visible = true) as faq_count,
        (SELECT COUNT(*) FROM photos WHERE client_id = c.client_id) as photo_count
      FROM clients c
      LEFT JOIN plans p ON p.name = c.plan_type
      WHERE c.auth_id = $1
    `

    // Send request to N8N webhook
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${jwtToken}`
      },
      body: JSON.stringify({
        operation: 'SELECT',
        sql: sql,
        params: [authId]
      })
    })

    if (!response.ok) {
      throw new Error(`Webhook request failed: ${response.status} ${response.statusText}`)
    }

    let webhookData = await response.json()
    
    // Handle array response format from N8N webhook
    if (Array.isArray(webhookData) && webhookData.length > 0) {
      webhookData = webhookData[0]
    }
    
    // Check webhook success/failure
    if (!webhookData.success) {
      throw new Error(webhookData.error_msg || 'Database operation failed')
    }

    // Get dashboard data from webhook body
    const row = webhookData.body
    
    if (!row || !row.client_id) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'No dashboard data found for user'
      }, { status: 404 })
    }

    // Now that we have client_id, check cache first
    const clientId = row.client_id
    cacheKey = `dashboard_${clientId}`
    cachedResult = serverCache.get(cacheKey)
    if (cachedResult) {
      return NextResponse.json({
        success: true,
        body: cachedResult,
        error_msg: null,
        cached: true
      })
    }


    const faqUsagePercentage = row.faq_limit > 0 ? Math.min((row.faq_count / row.faq_limit) * 100, 100) : 0
    const photoUsagePercentage = row.photo_limit > 0 ? Math.min((row.photo_count / row.photo_limit) * 100, 100) : 0

    const dashboardData = {
      clientInfo: {
        username: row.username,
        sector: row.sector,
        lang: row.lang,
        plan_type: row.plan_type,
        next_billing_date: row.next_billing_date
      },
      usageData: {
        usage_used: row.usage_used || 0,
        usage_limit: row.usage_limit || 100
      },
      knowledgeStats: {
        faqCount: row.faq_count || 0,
        photoCount: row.photo_count || 0,
        faqLimit: row.faq_limit || 0,
        photoLimit: row.photo_limit || 0,
        faqUsagePercentage: Math.round(faqUsagePercentage),
        photoUsagePercentage: Math.round(photoUsagePercentage)
      }
    }

    // Cache the result for 30 minutes
    serverCache.set(cacheKey, dashboardData, 30)

    return NextResponse.json({
      success: true,
      body: dashboardData,
      error_msg: null
    })
  } catch (error) {
    console.error('Error in dashboard data API:', error)
    return NextResponse.json(
      { 
        success: false,
        body: null,
        error_msg: error instanceof Error ? error.message : 'Internal server error'
      },
      { status: 500 }
    )
  }
}