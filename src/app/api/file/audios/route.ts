import { NextRequest, NextResponse } from 'next/server'
import { S3Client, PutObjectCommand, DeleteObjectCommand, ListObjectsV2Command } from '@aws-sdk/client-s3'
import { verifyAuth } from '@/utils/auth'

// Validate R2 environment variables
const validateR2Config = () => {
  const requiredEnvVars = [
    'CLOUDFLARE_R2_ENDPOINT',
    'CLOUDFLARE_R2_ACCESS_KEY_ID',
    'CLOUDFLARE_R2_SECRET_ACCESS_KEY',
    'CLOUDFLARE_R2_BUCKET_NAME',
    'CLOUDFLARE_R2_PUBLIC_URL'
  ]
  
  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      throw new Error(`Missing required environment variable: ${envVar}`)
    }
  }
}

// Validate configuration on module load
validateR2Config()

// Initialize R2 client with proper configuration for Cloudflare R2
const r2Client = new S3Client({
  region: 'auto', // R2 uses 'auto' region
  endpoint: process.env.CLOUDFLARE_R2_ENDPOINT!,
  forcePathStyle: true, // Required for R2 compatibility
  credentials: {
    accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID!,
    secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY!,
  },
})

const BUCKET_NAME = process.env.CLOUDFLARE_R2_BUCKET_NAME!
const PUBLIC_URL = process.env.CLOUDFLARE_R2_PUBLIC_URL!

// Helper function to determine content type from file extension
const getContentType = (fileName: string): string => {
  const ext = fileName.toLowerCase().split('.').pop()
  switch (ext) {
    case 'm4a':
      return 'audio/mp4'
    case 'mp3':
      return 'audio/mpeg'
    case 'mp4':
      return 'audio/mp4'
    case 'webm':
      return 'audio/webm'
    case 'ogg':
      return 'audio/ogg'
    case 'wav':
      return 'audio/wav'
    case 'aac':
      return 'audio/aac'
    default:
      return 'audio/mp4' // Default to mp4 for M4A files
  }
}

// POST - Upload audio files to R2
export async function POST(request: NextRequest) {
  try {
    // Authentication check
    const { authenticated, authId } = await verifyAuth()
    if (!authenticated || !authId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse form data
    const formData = await request.formData()
    const files = formData.getAll('files') as File[]
    const audioId = formData.get('audioId') as string

    if (!files || files.length === 0) {
      return NextResponse.json({ error: 'No files provided' }, { status: 400 })
    }

    if (!audioId) {
      return NextResponse.json({ error: 'Audio ID is required' }, { status: 400 })
    }

    const uploadResults = []

    // Upload each file to R2
    for (let i = 0; i < files.length; i++) {
      const file = files[i]
      const buffer = await file.arrayBuffer()
      
      // Generate unique filename
      const uniqueId = crypto.randomUUID()
      const fileName = `${audioId}-${uniqueId}.m4a` // Always use .m4a extension for converted files
      const filePath = `audios/${authId}/${fileName}`

      // Determine content type
      const contentType = getContentType(fileName)

      // Upload to R2
      const putCommand = new PutObjectCommand({
        Bucket: BUCKET_NAME,
        Key: filePath,
        Body: new Uint8Array(buffer),
        ContentType: contentType,
        CacheControl: '3600',
        ContentDisposition: `inline; filename="${fileName}"`, // Helps with audio playback
      })

      try {
        await r2Client.send(putCommand)
      } catch (uploadError) {
        console.error(`Failed to upload ${fileName} to R2:`, uploadError)
        throw uploadError
      }

      // Generate public URL
      const publicUrl = `${PUBLIC_URL}/${filePath}`

      uploadResults.push({
        fileName,
        filePath,
        publicUrl,
      })
    }

    return NextResponse.json({
      success: true,
      files: uploadResults,
      audioUrls: uploadResults.map(result => result.publicUrl),
      filePaths: uploadResults.map(result => result.filePath),
    })

  } catch (error) {
    console.error('Error uploading audio files to R2:', error)
    
    // Provide more specific error messages based on error type
    let errorMessage = 'Failed to upload audio files to R2'
    if (error instanceof Error) {
      if (error.message.includes('Missing required environment variable')) {
        errorMessage = `Configuration error: ${error.message}`
      } else if (error.message.includes('InternalError')) {
        errorMessage = 'R2 service error. Please check your R2 configuration (endpoint, credentials, bucket name)'
      } else if (error.message.includes('InvalidAccessKeyId')) {
        errorMessage = 'Invalid R2 access key ID'
      } else if (error.message.includes('SignatureDoesNotMatch')) {
        errorMessage = 'Invalid R2 secret access key'
      } else if (error.message.includes('NoSuchBucket')) {
        errorMessage = 'R2 bucket not found. Please check bucket name'
      } else {
        errorMessage = `R2 upload error: ${error.message}`
      }
    }
    
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    )
  }
}

// DELETE - Delete audio files from R2
export async function DELETE(request: NextRequest) {
  try {
    // Authentication check
    const { authenticated, authId } = await verifyAuth()
    if (!authenticated || !authId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse request body
    const body = await request.json()
    const { filePaths } = body

    if (!filePaths || !Array.isArray(filePaths)) {
      return NextResponse.json({ error: 'File paths array is required' }, { status: 400 })
    }

    // Delete each file from R2
    for (const filePath of filePaths) {
      // Security check: ensure file path belongs to current auth user's audios folder
      if (!filePath.startsWith(`audios/${authId}/`)) {
        console.error(`Unauthorized file access attempt: ${filePath} for auth ${authId}`)
        continue
      }

      const deleteCommand = new DeleteObjectCommand({
        Bucket: BUCKET_NAME,
        Key: filePath,
      })

      try {
        await r2Client.send(deleteCommand)
      } catch (error) {
        console.error(`Error deleting audio file ${filePath}:`, error)
        // Continue with other files even if one fails
      }
    }

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('Error deleting audio files from R2:', error)
    return NextResponse.json(
      { error: 'Failed to delete audio files from R2' },
      { status: 500 }
    )
  }
}

// GET - List audio files and get URLs
export async function GET() {
  try {
    // Authentication check
    const { authenticated, authId } = await verifyAuth()
    if (!authenticated || !authId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // List files for current auth user's audios folder
    const listCommand = new ListObjectsV2Command({
      Bucket: BUCKET_NAME,
      Prefix: `audios/${authId}/`,
    })

    const response = await r2Client.send(listCommand)
    
    const files = response.Contents?.map(object => ({
      key: object.Key,
      publicUrl: `${PUBLIC_URL}/${object.Key}`,
      size: object.Size,
      lastModified: object.LastModified,
    })) || []

    return NextResponse.json({ 
      success: true,
      files
    })

  } catch (error) {
    console.error('Error getting audio files from R2:', error)
    
    let errorMessage = 'Failed to get audio files from R2'
    if (error instanceof Error) {
      if (error.message.includes('Missing required environment variable')) {
        errorMessage = `Configuration error: ${error.message}`
      } else {
        errorMessage = `R2 error: ${error.message}`
      }
    }
    
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    )
  }
}

// PUT - Update audio file metadata (if needed for future features)
export async function PUT(request: NextRequest) {
  try {
    // Authentication check
    const { authenticated, authId } = await verifyAuth()
    if (!authenticated || !authId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse request body
    const body = await request.json()
    const { filePath } = body

    if (!filePath) {
      return NextResponse.json({ error: 'File path is required' }, { status: 400 })
    }

    // Security check: ensure file path belongs to current auth user's audios folder
    if (!filePath.startsWith(`audios/${authId}/`)) {
      return NextResponse.json({ error: 'Unauthorized file access' }, { status: 403 })
    }

    // For now, just return success as R2 doesn't support metadata updates
    // without re-uploading. This endpoint is reserved for future features.
    return NextResponse.json({ 
      success: true,
      message: 'Audio file metadata update not implemented yet'
    })

  } catch (error) {
    console.error('Error updating audio file metadata:', error)
    return NextResponse.json(
      { error: 'Failed to update audio file metadata' },
      { status: 500 }
    )
  }
}