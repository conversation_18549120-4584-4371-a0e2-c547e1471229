import { NextRequest, NextResponse } from 'next/server'
import { S3Client, PutObjectCommand, DeleteObjectCommand, ListObjectsV2Command } from '@aws-sdk/client-s3'
import { verifyAuth } from '@/utils/auth'

// Validate R2 environment variables
const validateR2Config = () => {
  const requiredEnvVars = [
    'CLOUDFLARE_R2_ENDPOINT',
    'CLOUDFLARE_R2_ACCESS_KEY_ID',
    'CLOUDFLARE_R2_SECRET_ACCESS_KEY',
    'CLOUDFLARE_R2_BUCKET_NAME',
    'CLOUDFLARE_R2_PUBLIC_URL'
  ]
  
  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      throw new Error(`Missing required environment variable: ${envVar}`)
    }
  }
}

// Validate configuration on module load
validateR2Config()

// Initialize R2 client with proper configuration for Cloudflare R2
const r2Client = new S3Client({
  region: 'auto', // R2 uses 'auto' region
  endpoint: process.env.CLOUDFLARE_R2_ENDPOINT!,
  forcePathStyle: true, // Required for R2 compatibility
  credentials: {
    accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID!,
    secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY!,
  },
})

const BUCKET_NAME = process.env.CLOUDFLARE_R2_BUCKET_NAME!
const PUBLIC_URL = process.env.CLOUDFLARE_R2_PUBLIC_URL!

// POST - Upload files to R2
export async function POST(request: NextRequest) {
  try {
    // Authentication check
    const { authenticated, authId } = await verifyAuth()
    if (!authenticated || !authId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse form data
    const formData = await request.formData()
    const files = formData.getAll('files') as File[]
    const photoId = formData.get('photoId') as string

    if (!files || files.length === 0) {
      return NextResponse.json({ error: 'No files provided' }, { status: 400 })
    }

    if (!photoId) {
      return NextResponse.json({ error: 'Photo ID is required' }, { status: 400 })
    }

    // Upload all files to R2 in parallel
    const uploadPromises = files.map(async (file, index) => {
      const buffer = await file.arrayBuffer()
      
      // Generate unique filename (same logic as original)
      const uniqueId = crypto.randomUUID()
      const fileName = `${photoId}-${uniqueId}.jpg`
      const filePath = `photos/${authId}/${fileName}`

      // Upload to R2
      const putCommand = new PutObjectCommand({
        Bucket: BUCKET_NAME,
        Key: filePath,
        Body: new Uint8Array(buffer),
        ContentType: 'image/jpeg',
        CacheControl: '3600',
      })

      try {
        await r2Client.send(putCommand)
      } catch (uploadError) {
        console.error(`Failed to upload ${fileName} to R2:`, uploadError)
        throw uploadError
      }

      // Generate public URL
      const publicUrl = `${PUBLIC_URL}/${filePath}`

      return {
        fileName,
        filePath,
        publicUrl,
      }
    })

    // Wait for all uploads to complete in parallel
    const uploadResults = await Promise.all(uploadPromises)

    return NextResponse.json({
      success: true,
      files: uploadResults,
      photoUrls: uploadResults.map(result => result.publicUrl),
      filePaths: uploadResults.map(result => result.filePath),
    })

  } catch (error) {
    console.error('Error uploading files to R2:', error)
    
    // Provide more specific error messages based on error type
    let errorMessage = 'Failed to upload files to R2'
    if (error instanceof Error) {
      if (error.message.includes('Missing required environment variable')) {
        errorMessage = `Configuration error: ${error.message}`
      } else if (error.message.includes('InternalError')) {
        errorMessage = 'R2 service error. Please check your R2 configuration (endpoint, credentials, bucket name)'
      } else if (error.message.includes('InvalidAccessKeyId')) {
        errorMessage = 'Invalid R2 access key ID'
      } else if (error.message.includes('SignatureDoesNotMatch')) {
        errorMessage = 'Invalid R2 secret access key'
      } else if (error.message.includes('NoSuchBucket')) {
        errorMessage = 'R2 bucket not found. Please check bucket name'
      } else {
        errorMessage = `R2 upload error: ${error.message}`
      }
    }
    
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    )
  }
}

// DELETE - Delete files from R2
export async function DELETE(request: NextRequest) {
  try {
    // Authentication check
    const { authenticated, authId } = await verifyAuth()
    if (!authenticated || !authId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse request body
    const body = await request.json()
    const { filePaths } = body

    if (!filePaths || !Array.isArray(filePaths)) {
      return NextResponse.json({ error: 'File paths array is required' }, { status: 400 })
    }

    // Delete each file from R2
    for (const filePath of filePaths) {
      // Security check: ensure file path belongs to current auth user's photos folder
      if (!filePath.startsWith(`photos/${authId}/`)) {
        console.error(`Unauthorized file access attempt: ${filePath} for auth ${authId}`)
        continue
      }

      const deleteCommand = new DeleteObjectCommand({
        Bucket: BUCKET_NAME,
        Key: filePath,
      })

      try {
        await r2Client.send(deleteCommand)
      } catch (error) {
        console.error(`Error deleting file ${filePath}:`, error)
        // Continue with other files even if one fails
      }
    }

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('Error deleting files from R2:', error)
    return NextResponse.json(
      { error: 'Failed to delete files from R2' },
      { status: 500 }
    )
  }
}

// GET - List files and get URLs
export async function GET() {
  try {
    // Authentication check
    const { authenticated, authId } = await verifyAuth()
    if (!authenticated || !authId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // List files for current auth user's photos folder
    const listCommand = new ListObjectsV2Command({
      Bucket: BUCKET_NAME,
      Prefix: `photos/${authId}/`,
    })

    const response = await r2Client.send(listCommand)
    
    const files = response.Contents?.map(object => ({
      key: object.Key,
      publicUrl: `${PUBLIC_URL}/${object.Key}`,
    })) || []

    return NextResponse.json({ 
      success: true,
      files
    })

  } catch (error) {
    console.error('Error getting files from R2:', error)
    
    let errorMessage = 'Failed to get files from R2'
    if (error instanceof Error) {
      if (error.message.includes('Missing required environment variable')) {
        errorMessage = `Configuration error: ${error.message}`
      } else {
        errorMessage = `R2 error: ${error.message}`
      }
    }
    
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    )
  }
}