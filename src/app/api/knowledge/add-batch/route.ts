import { NextResponse } from 'next/server'
import { verifyAuth } from '@/utils/auth'
import { generateWebhookToken } from '@/utils/jwt'
import { nanoid } from 'nanoid'
import { getM4AExtension } from '@/utils/audioConversion'
import { serverCache } from '@/lib/cache'

// Helper function to chunk array
function chunkArray<T>(array: T[], chunkSize: number): T[][] {
  const chunks: T[][] = []
  for (let i = 0; i < array.length; i += chunkSize) {
    chunks.push(array.slice(i, i + chunkSize))
  }
  return chunks
}

export async function POST(request: Request) {
  try {
    // Verify authentication
    const { authenticated, authId, clientId } = await verifyAuth()
    if (!authenticated || !authId) {
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Unauthorized'
      }, { status: 401 })
    }

    // Validate clientId exists
    if (!clientId) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Client ID not found in authentication'
      }, { status: 400 })
    }

    // Get the secure webhook URL from environment
    const webhookUrl = process.env.CHHLAT_DB_WEBHOOK_URL
    
    if (!webhookUrl) {
      console.warn('Secure webhook URL not configured')
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Database webhook not configured'
      }, { status: 500 })
    }

    // Parse FormData request
    const formData = await request.formData()
    const faqBatchString = formData.get('faqBatch') as string
    
    if (!faqBatchString) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Missing required field: faqBatch'
      }, { status: 400 })
    }

    const faqBatch = JSON.parse(faqBatchString)

    // Validate required fields
    if (!faqBatch || !Array.isArray(faqBatch) || faqBatch.length === 0) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Missing required field: faqBatch'
      }, { status: 400 })
    }


    // Validate batch size (max 20 FAQs)
    const MAX_BATCH_SIZE = 20
    if (faqBatch.length > MAX_BATCH_SIZE) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: `Batch size ${faqBatch.length} exceeds limit of ${MAX_BATCH_SIZE}`
      }, { status: 400 })
    }

    // Validate each FAQ in the batch
    for (let i = 0; i < faqBatch.length; i++) {
      const faq = faqBatch[i]
      
      if (!faq.question || typeof faq.question !== 'string' || faq.question.trim() === '') {
        return NextResponse.json({
          success: false,
          body: null,
          error_msg: `FAQ ${i + 1}: Question is required`
        }, { status: 400 })
      }

      if (!faq.isAudioAnswer && (!faq.answer || typeof faq.answer !== 'string' || faq.answer.trim() === '')) {
        return NextResponse.json({
          success: false,
          body: null,
          error_msg: `FAQ ${i + 1}: Answer is required for text FAQs`
        }, { status: 400 })
      }

      if (faq.isAudioAnswer && (!faq.answer || typeof faq.answer !== 'string' || faq.answer.trim() === '')) {
        return NextResponse.json({
          success: false,
          body: null,
          error_msg: `FAQ ${i + 1}: Audio code is required for audio FAQs`
        }, { status: 400 })
      }
      
      // Validate audio blob exists for audio FAQs
      if (faq.isAudioAnswer && faq.audioIndex !== null) {
        const audioBlob = formData.get(`audioBlob_${faq.audioIndex}`)
        if (!audioBlob) {
          return NextResponse.json({
            success: false,
            body: null,
            error_msg: `FAQ ${i + 1}: Missing processed audio blob for audio FAQ`
          }, { status: 400 })
        }
      }
    }

    // Generate batch ID 
    const batchId = nanoid(12)
    
    // Step 1: Split FAQs by type
    const textFaqs = faqBatch.filter(faq => !faq.isAudioAnswer)
    const audioFaqs = faqBatch.filter(faq => faq.isAudioAnswer)
    
    
    // Step 2: Process audio FAQs with pre-processed blobs (parallel R2 upload)
    const processedAudioFaqs: Array<{
      faq_id: string
      question: string
      answer: string
      audio_url: string | null
      audio_file_path: string | null
      photo_id: string | null
      photo_urls: string[] | null
      is_audio: boolean
      success: boolean
      error?: string
    }> = []
    if (audioFaqs.length > 0) {
      const audioChunks = chunkArray(audioFaqs, 5)
      
      for (let chunkIndex = 0; chunkIndex < audioChunks.length; chunkIndex++) {
        const chunk = audioChunks[chunkIndex]
        
        // Process this chunk in parallel
        const chunkResults = await Promise.all(
          chunk.map(async (faq) => {
            try {
              // Get pre-processed audio blob from FormData
              const audioBlob = faq.audioIndex !== null ? formData.get(`audioBlob_${faq.audioIndex}`) as File : null
              
              if (!audioBlob) {
                throw new Error('Missing processed audio blob for audio FAQ')
              }
              
              // Generate audio ID and prepare for upload
              const audioCode = faq.answer // Use the audio code as the base
              const uniqueId = nanoid(8)
              const audioId = `${audioCode}-${uniqueId}`
              const fileExtension = getM4AExtension()
              
              // Upload to R2 using the audio API
              const uploadFormData = new FormData()
              const audioFile = new File([audioBlob], `${audioId}.${fileExtension}`, {
                type: 'audio/x-m4a'
              })
              uploadFormData.append('files', audioFile)
              uploadFormData.append('audioId', audioId)
              
              const uploadResponse = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/file/audios`, {
                method: 'POST',
                body: uploadFormData,
                headers: {
                  'Cookie': request.headers.get('Cookie') || ''
                }
              })
              
              if (!uploadResponse.ok) {
                const errorData = await uploadResponse.json()
                throw new Error(`R2 upload failed: ${errorData.error || 'Unknown error'}`)
              }
              
              const uploadResult = await uploadResponse.json()
              if (!uploadResult.success || !uploadResult.audioUrls || uploadResult.audioUrls.length === 0) {
                throw new Error('R2 upload succeeded but no URLs returned')
              }
              
              // Return processed FAQ with audio URL
              return {
                faq_id: nanoid(16),
                question: faq.question,
                answer: '', // Empty for audio FAQs
                audio_url: uploadResult.audioUrls[0],
                audio_file_path: uploadResult.filePaths[0],
                photo_id: faq.photoInfo?.photo_id || null,
                photo_urls: faq.photoInfo?.full_photo_urls || null,
                is_audio: true,
                success: true
              }
              
            } catch (error) {
              console.error(`Error processing audio FAQ "${faq.question}":`, error)
              return {
                faq_id: nanoid(16),
                question: faq.question,
                answer: '',
                audio_url: null,
                audio_file_path: null,
                photo_id: faq.photoInfo?.photo_id || null,
                photo_urls: faq.photoInfo?.full_photo_urls || null,
                is_audio: true,
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
              }
            }
          })
        )
        
        processedAudioFaqs.push(...chunkResults)
      }
    }
    
    // Step 3: Prepare text FAQs (no processing needed)
    const processedTextFaqs = textFaqs.map(faq => ({
      faq_id: nanoid(16),
      question: faq.question,
      answer: faq.answer,
      audio_url: null,
      audio_file_path: null,
      photo_id: faq.photoInfo?.photo_id || null,
      photo_urls: faq.photoInfo?.full_photo_urls || null,
      is_audio: false,
      success: true
    }))
    
    // Step 4: Combine all processed FAQs
    const allProcessedFaqs = [...processedTextFaqs, ...processedAudioFaqs]
    const successfulFaqs = allProcessedFaqs.filter(faq => faq.success)
    const failedFaqs = allProcessedFaqs.filter(faq => !faq.success)
    

    // Generate JWT token for webhook authentication
    const jwtToken = generateWebhookToken()

    // Step 5: Get client info for webhook payload
    let clientInfo = null
    try {
      const knowledgeCacheKey = `knowledge_${authId}`
      const knowledgeData = serverCache.get(knowledgeCacheKey)
      if (knowledgeData) {
        clientInfo = {
          sector: knowledgeData.sector,
          lang: knowledgeData.clientLang
        }
      }
    } catch (error) {
      console.warn('Could not get client info from cache:', error)
    }

    // Step 6: Send successful FAQs to N8N webhook (if any)
    let webhookResult = null
    if (successfulFaqs.length > 0) {
      const webhookResponse = await fetch(webhookUrl, {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${jwtToken}`
        },
        body: JSON.stringify({
          operation: 'add_batch',
          mode: 'faq',
          batch_id: batchId,
          batch_size: successfulFaqs.length,
          client_id: clientId,
          client_info: {
            sector: clientInfo?.sector || null,
            lang: clientInfo?.lang || null
          },
          faqs: successfulFaqs.map(faq => ({
            faq_id: faq.faq_id,
            question: faq.question,
            answer: faq.answer,
            audio_url: faq.audio_url,
            audio_file_path: faq.audio_file_path,
            photo_id: faq.photo_id,
            photo_urls: faq.photo_urls,
            is_audio: faq.is_audio
          }))
        })
      })

      if (!webhookResponse.ok) {
        throw new Error(`Webhook request failed: ${webhookResponse.status} ${webhookResponse.statusText}`)
      }

      webhookResult = await webhookResponse.json()
      
      // Handle array response format from N8N webhook
      if (Array.isArray(webhookResult) && webhookResult.length > 0) {
        webhookResult = webhookResult[0]
      }
      
      // More flexible webhook response handling
      if (webhookResult && webhookResult.success === false) {
        throw new Error(webhookResult.error_msg || 'Database processing failed')
      }
      // If webhook doesn't have success field or success is true, continue
    }

    // Step 7: Update knowledge cache with new FAQ count (if cache exists)
    try {
      const knowledgeCacheKey = `knowledge_${authId}`
      const knowledgeData = serverCache.get(knowledgeCacheKey)
      
      if (knowledgeData) {
        // Update FAQ count in cache
        const currentFaqCount = knowledgeData.knowledgeStats.faqCount || 0
        const newFaqCount = currentFaqCount + successfulFaqs.length
        const faqLimit = knowledgeData.knowledgeStats.faqLimit || 1
        
        knowledgeData.knowledgeStats.faqCount = newFaqCount
        knowledgeData.knowledgeStats.faqUsagePercentage = Math.round((newFaqCount / faqLimit) * 100)
        
        serverCache.set(knowledgeCacheKey, knowledgeData, 30)
      } else {
      }
    } catch (error) {
      console.warn('Could not update knowledge cache:', error)
    }

    // Step 8: Return combined results
    return NextResponse.json({
      success: true,
      body: {
        items_processed: successfulFaqs.length,
        items_failed: failedFaqs.length,
        total_items: allProcessedFaqs.length,
        batch_id: batchId,
        failures: failedFaqs.map(faq => ({
          question: faq.question,
          error: faq.error
        })),
        webhook_result: webhookResult?.body || null
      },
      error_msg: failedFaqs.length > 0 ? `${failedFaqs.length} items failed to process` : null
    })

  } catch (error: unknown) {
    console.error('Error in FAQ batch add API:', error)
    return NextResponse.json(
      { 
        success: false,
        body: null,
        error_msg: error instanceof Error ? error.message : 'Internal server error'
      },
      { status: 500 }
    )
  }
}