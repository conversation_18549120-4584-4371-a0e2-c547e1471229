import { NextRequest, NextResponse } from 'next/server'
import { verifyAuth } from '@/utils/auth'
import { generateWebhookToken } from '@/utils/jwt'
import { nanoid } from 'nanoid'
import { audioRedis } from '@/utils/redis'
import { telegramAudio } from '@/utils/telegram'
import { convertToMP4A, validateAudioBuffer, getM4AExtension } from '@/utils/audioConversion'
import { serverCache } from '@/lib/cache'

// Type definitions (for reference, not actively used)
// type KnowledgeItem = {
//   id: number
//   question_p: string
//   answer_p: string
//   created_at: string
//   updated_at: string
//   audio_url?: string
//   audio_duration?: number
//   photo_url?: unknown
//   photo_id?: string
//   audio_file_path?: string
//   auth_id: string
// }

// GET - Fetch all knowledge items for the client
export async function GET() {
  try {
    // Authentication check
    const { authenticated, authId, clientId } = await verifyAuth()
    if (!authenticated || !authId) {
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Unauthorized'
      }, { status: 401 })
    }

    // Validate clientId exists
    if (!clientId) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Client ID not found in authentication'
      }, { status: 400 })
    }

    // Get the secure webhook URL from environment
    const webhookUrl = process.env.CHHLAT_DB_WEBHOOK_URL
    
    if (!webhookUrl) {
      console.warn('Secure webhook URL not configured')
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Database webhook not configured'
      }, { status: 500 })
    }

    // Generate JWT token for webhook authentication
    const jwtToken = generateWebhookToken()

    // Fetch knowledge items using PostgreSQL via webhook
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${jwtToken}`
      },
      body: JSON.stringify({
        operation: 'SELECT',
        sql: `
          SELECT id, faq_id, question_p, answer_p, created_at, updated_at, 
                 audio_url, audio_duration, photo_url, photo_id, audio_file_path
          FROM faqs 
          WHERE client_id = $1 AND is_visible = true 
          ORDER BY updated_at DESC
        `,
        params: [clientId]
      })
    })

    if (!response.ok) {
      throw new Error(`Webhook request failed: ${response.status} ${response.statusText}`)
    }

    let webhookData = await response.json()
    
    // Handle array response format from N8N webhook
    if (Array.isArray(webhookData) && webhookData.length > 0) {
      webhookData = webhookData[0]
    }
    
    if (!webhookData.success) {
      throw new Error(webhookData.error_msg || 'Database operation failed')
    }

    const data = webhookData.body || []

    // Process data for frontend
    const processedData = data?.map((item: any) => ({
      ...item,
      question: item.question_p || '',
      answer: item.answer_p || ''
    })) || []

    return NextResponse.json({ 
      success: true,
      body: processedData,
      error_msg: null
    })

  } catch (error) {
    console.error('Error in knowledge lists GET API:', error)
    return NextResponse.json({
      success: false,
      body: null,
      error_msg: 'Internal server error'
    }, { status: 500 })
  }
}


// DELETE - Delete a knowledge item
export async function DELETE(request: NextRequest) {
  try {
    // Authentication check
    const { authenticated, authId, clientId } = await verifyAuth()
    if (!authenticated || !authId) {
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Unauthorized'
      }, { status: 401 })
    }

    // Validate clientId exists
    if (!clientId) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Client ID not found in authentication'
      }, { status: 400 })
    }

    // Get faq_id from URL search params
    const { searchParams } = new URL(request.url)
    const faq_id = searchParams.get('faq_id')

    if (!faq_id) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Missing required parameter: faq_id'
      }, { status: 400 })
    }

    // Parse request body to get audio_file_path
    const body = await request.json()
    const { audio_file_path } = body

    // Get the secure webhook URL from environment
    const webhookUrl = process.env.CHHLAT_DB_WEBHOOK_URL
    
    if (!webhookUrl) {
      console.warn('Secure webhook URL not configured')
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Database webhook not configured'
      }, { status: 500 })
    }

    // Generate JWT token for webhook authentication
    const jwtToken = generateWebhookToken()

    // If FAQ has audio file, delete it from R2 before database deletion
    if (audio_file_path) {
      
      try {
        const deleteResponse = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/file/audios`, {
          method: 'DELETE',
          headers: { 
            'Content-Type': 'application/json',
            'Cookie': request.headers.get('Cookie') || ''
          },
          body: JSON.stringify({ filePaths: [audio_file_path] })
        })
        
        if (!deleteResponse.ok) {
          console.error('Failed to delete audio file from R2:', audio_file_path)
          // Don't fail the operation if cleanup fails
        } else {
        }
      } catch (cleanupError) {
        console.error('Error during audio cleanup:', cleanupError)
        // Don't fail the operation if cleanup fails
      }
    }

    // Delete the knowledge item using PostgreSQL via webhook
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${jwtToken}`
      },
      body: JSON.stringify({
        operation: 'DELETE',
        sql: `
          DELETE FROM faqs 
          WHERE client_id = $1 AND faq_id = $2
          RETURNING *
        `,
        params: [clientId, faq_id]
      })
    })

    if (!response.ok) {
      throw new Error(`Webhook request failed: ${response.status} ${response.statusText}`)
    }

    let webhookData = await response.json()
    
    // Handle array response format from N8N webhook
    if (Array.isArray(webhookData) && webhookData.length > 0) {
      webhookData = webhookData[0]
    }
    
    if (!webhookData.success) {
      throw new Error(webhookData.error_msg || 'Database operation failed')
    }

    const deletedData = webhookData.body || []

    if (!deletedData || deletedData.length === 0) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Knowledge item not found or already deleted'
      }, { status: 404 })
    }

    // Update knowledge cache with decreased FAQ count (if cache exists)
    try {
      const knowledgeCacheKey = `knowledge_${authId}`
      const knowledgeData = serverCache.get(knowledgeCacheKey)
      
      if (knowledgeData) {
        // Update FAQ count in cache (subtract 1)
        const currentFaqCount = knowledgeData.knowledgeStats.faqCount || 0
        const newFaqCount = Math.max(currentFaqCount - 1, 0) // Ensure >= 0
        const faqLimit = knowledgeData.knowledgeStats.faqLimit || 1
        
        knowledgeData.knowledgeStats.faqCount = newFaqCount
        knowledgeData.knowledgeStats.faqUsagePercentage = Math.round((newFaqCount / faqLimit) * 100)
        
        serverCache.set(knowledgeCacheKey, knowledgeData, 30)
      } else {
      }
    } catch (error) {
      console.warn('Could not update knowledge cache:', error)
    }

    return NextResponse.json({ 
      success: true,
      body: deletedData,
      error_msg: null
    })

  } catch (error) {
    console.error('Error in knowledge lists DELETE API:', error)
    return NextResponse.json({
      success: false,
      body: null,
      error_msg: 'Internal server error'
    }, { status: 500 })
  }
}

// PUT - Update a knowledge item (following intro management pattern)
export async function PUT(request: NextRequest) {
  try {
    // Authentication check
    const { authenticated, authId, clientId } = await verifyAuth()
    if (!authenticated || !authId) {
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Unauthorized'
      }, { status: 401 })
    }

    // Validate clientId exists
    if (!clientId) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Client ID not found in authentication'
      }, { status: 400 })
    }

    // Parse request body
    const { faq_id, updateData, sector, lang } = await request.json()

    if (!faq_id || !updateData || !sector || !lang) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Missing required parameters: faq_id, updateData, sector, lang'
      }, { status: 400 })
    }

    // Get the secure webhook URL from environment
    const webhookUrl = process.env.CHHLAT_DB_WEBHOOK_URL
    
    if (!webhookUrl) {
      console.warn('Secure webhook URL not configured')
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Database webhook not configured'
      }, { status: 500 })
    }

    // Generate JWT token for webhook authentication
    const jwtToken = generateWebhookToken()

    // Handle audio processing if needed
    if (updateData.hasAudioCodeChanged && updateData.isAudioAnswer && updateData.audioCode) {

      // Delete old audio file from R2 if it exists
      if (updateData.originalAudioFilePath) {
        try {
          const deleteResponse = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/file/audios`, {
            method: 'DELETE',
            headers: {
              'Content-Type': 'application/json',
              'Cookie': request.headers.get('Cookie') || ''
            },
            body: JSON.stringify({ filePaths: [updateData.originalAudioFilePath] })
          })

          if (!deleteResponse.ok) {
            console.error('Failed to delete old audio file from R2:', updateData.originalAudioFilePath)
            // Don't fail the operation if cleanup fails
          }
        } catch (cleanupError) {
          console.error('Error during old audio cleanup:', cleanupError)
          // Don't fail the operation if cleanup fails
        }
      }

      // Get audio data from Redis
      const audioData = await audioRedis.getAudioData(updateData.audioCode)

      if (!audioData) {
        return NextResponse.json({
          success: false,
          body: null,
          error_msg: `Audio code ${updateData.audioCode} not found or expired`
        }, { status: 400 })
      }

      try {
        // Download audio from Telegram
        const { buffer, mimeType } = await telegramAudio.downloadAudioFile(audioData.file_id)

        // Validate audio buffer
        const validation = validateAudioBuffer(buffer, mimeType)
        if (!validation.valid) {
          throw new Error(`Invalid audio data: ${validation.error}`)
        }

        // Convert audio to MP4A AAC format
        const conversionResult = await convertToMP4A(buffer, mimeType)

        // Generate audio ID from the audio code with unique suffix
        const audioCode = updateData.audioCode
        const uniqueId = nanoid(8)
        const audioId = `${audioCode}-${uniqueId}`
        const fileExtension = getM4AExtension()

        // Upload to R2 using the audio API
        const formData = new FormData()
        const audioFile = new File([conversionResult.buffer], `${audioId}.${fileExtension}`, {
          type: conversionResult.mimeType
        })
        formData.append('files', audioFile)
        formData.append('audioId', audioId)

        const uploadResponse = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/file/audios`, {
          method: 'POST',
          body: formData,
          headers: {
            'Cookie': request.headers.get('Cookie') || ''
          }
        })

        if (!uploadResponse.ok) {
          const errorData = await uploadResponse.json()
          throw new Error(`R2 upload failed: ${errorData.error || 'Unknown error'}`)
        }

        const uploadResult = await uploadResponse.json()
        if (!uploadResult.success || !uploadResult.audioUrls || uploadResult.audioUrls.length === 0) {
          throw new Error('R2 upload succeeded but no URLs returned')
        }

        // Update the updateData with audio information
        updateData.audio_url = uploadResult.audioUrls[0]
        updateData.audio_file_path = uploadResult.filePaths[0]


      } catch (audioError: unknown) {
        console.error('Error processing audio:', audioError)
        return NextResponse.json({
          success: false,
          body: null,
          error_msg: `Failed to process audio: ${audioError instanceof Error ? audioError.message : 'Unknown error'}`
        }, { status: 500 })
      }
    } else if (updateData.originalAudioFilePath && updateData.isAudioAnswer === false) {
      // Handle cleanup when switching from audio to text mode
      try {
        const deleteResponse = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/file/audios`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
            'Cookie': request.headers.get('Cookie') || ''
          },
          body: JSON.stringify({ filePaths: [updateData.originalAudioFilePath] })
        })

        if (!deleteResponse.ok) {
          console.error('Failed to delete audio file when switching to text mode:', updateData.originalAudioFilePath)
          // Don't fail the operation if cleanup fails
        }

        // Clear audio-related fields
        updateData.audio_url = null
        updateData.audio_file_path = null
      } catch (cleanupError) {
        console.error('Error during audio cleanup when switching to text mode:', cleanupError)
        // Don't fail the operation if cleanup fails
      }
    }

    // Remove context fields that don't exist in faqs table before database update
    const { isAudioAnswer, audioCode, hasAudioCodeChanged, originalAudioFilePath, is_audio, onlyPhoto, ...dbUpdateData } = updateData

    // Prepare update fields and values for PostgreSQL
    const updateFields: string[] = []
    const updateValues: unknown[] = []
    let valueIndex = 3

    // Build dynamic update query based on changed fields
    Object.entries(dbUpdateData).forEach(([key, value]) => {
      updateFields.push(`${key} = $${valueIndex}`)
      updateValues.push(value)
      valueIndex++
    })

    if (updateFields.length === 0) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'No fields to update'
      }, { status: 400 })
    }

    // Extract helper fields for webhook processing
    const helperAnswer = updateData.answer_p || null;
    const helperAudioUrl = updateData.audio_url || null;

    // Update FAQ using PostgreSQL via webhook
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${jwtToken}`
      },
      body: JSON.stringify({
        operation: 'UPDATE',
        mode: 'faq',
        sector: sector || null,
        lang: lang || null,
        is_audio: updateData.is_audio || false,
        onlyPhoto: updateData.onlyPhoto || false,
        // Helper fields for easier webhook processing
        clientId: clientId,
        faq_id: faq_id,
        answer_p: helperAnswer,
        audio_url: helperAudioUrl,
        sql: `
          UPDATE faqs 
          SET ${updateFields.join(', ')}
          WHERE client_id = $1 AND faq_id = $2
        `,
        params: [clientId, faq_id, ...updateValues]
      })
    })

    if (!response.ok) {
      throw new Error(`Webhook request failed: ${response.status} ${response.statusText}`)
    }

    let webhookData = await response.json()
    
    // Handle array response format from N8N webhook
    if (Array.isArray(webhookData) && webhookData.length > 0) {
      webhookData = webhookData[0]
    }
    
    if (!webhookData.success) {
      throw new Error(webhookData.error_msg || 'Database operation failed')
    }

    // No cache update needed - knowledge data is fetched fresh each time
    // Frontend handles optimistic UI updates with local state

    // Return the new audio URL if audio was processed
    const responseBody = updateData.hasAudioCodeChanged && updateData.audio_url ? {
      audio_url: updateData.audio_url,
      audio_file_path: updateData.audio_file_path
    } : null

    return NextResponse.json({
      success: true,
      body: responseBody,
      error_msg: null
    })

  } catch (error: unknown) {
    console.error('Error in knowledge lists PUT API:', error)
    return NextResponse.json(
      { 
        success: false,
        body: null,
        error_msg: error instanceof Error ? error.message : 'Internal server error'
      },
      { status: 500 }
    )
  }
}
