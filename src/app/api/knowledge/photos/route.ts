import { NextRequest, NextResponse } from 'next/server'
import { verifyAuth } from '@/utils/auth'
import { generateWebhookToken } from '@/utils/jwt'
import { serverCache } from '@/lib/cache'


// POST - Create new photo
export async function POST(request: NextRequest) {
  try {
    // Verify authentication
    const { authenticated, authId, clientId } = await verifyAuth()
    if (!authenticated || !authId) {
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Unauthorized'
      }, { status: 401 })
    }

    // Validate clientId exists
    if (!clientId) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Client ID not found in authentication'
      }, { status: 400 })
    }

    // Get the secure webhook URL from environment
    const webhookUrl = process.env.CHHLAT_DB_WEBHOOK_URL
    
    if (!webhookUrl) {
      console.warn('Secure webhook URL not configured')
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Database webhook not configured'
      }, { status: 500 })
    }

    // Parse request body
    const body = await request.json()
    const { photo_id, photo_url, photo_file_path } = body

    if (!photo_id || !photo_url || !photo_file_path) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Missing required fields: photo_id, photo_url, photo_file_path'
      }, { status: 400 })
    }

    // Get knowledge cache data for later cache update
    const knowledgeCacheKey = `knowledge_${authId}`
    const knowledgeData = serverCache.get(knowledgeCacheKey)

    // Generate JWT token for webhook authentication
    const jwtToken = generateWebhookToken()

    // Insert photo into PostgreSQL database via webhook
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${jwtToken}`
      },
      body: JSON.stringify({
        operation: 'INSERT',
        sql: `
          INSERT INTO photos (
            photo_id, client_id, photo_url, photo_file_path
          ) VALUES (
            $1, $2, $3, $4
          )
          RETURNING id
        `,
        params: [
          photo_id,
          clientId,
          photo_url,
          photo_file_path
        ]
      })
    })

    if (!response.ok) {
      throw new Error(`Webhook request failed: ${response.status} ${response.statusText}`)
    }

    let webhookData = await response.json()
    
    // Handle array response format from N8N webhook
    if (Array.isArray(webhookData) && webhookData.length > 0) {
      webhookData = webhookData[0]
    }
    
    if (!webhookData.success) {
      throw new Error(webhookData.error_msg || 'Database operation failed')
    }

    // Extract single result from body array (webhook body is always an array)
    const insertResult = webhookData.body?.[0]

    if (!insertResult || !insertResult.id) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Failed to insert photo - no ID returned'
      }, { status: 500 })
    }

    // Update knowledge cache after successful DB insert (only if cache exists)
    const newPhotoId = insertResult.id
    
    if (knowledgeData) {
      // Add new photo to beginning of photos array
      const newPhoto = {
        id: newPhotoId,
        photo_id: photo_id,
        photo_url: photo_url,
        photo_file_path: photo_file_path,
        updated_at: new Date().toISOString()
      }
      
      const updatedPhotos = [newPhoto, ...(knowledgeData.photos || [])]
      const newPhotoCount = updatedPhotos.length
      const photoLimit = knowledgeData.knowledgeStats.photoLimit || 1
      
      // Update knowledge cache with new photo and updated stats
      knowledgeData.photos = updatedPhotos
      knowledgeData.knowledgeStats.photoCount = newPhotoCount
      knowledgeData.knowledgeStats.photoUsagePercentage = Math.round((newPhotoCount / photoLimit) * 100)
      
      serverCache.set(knowledgeCacheKey, knowledgeData, 30)
    }

    return NextResponse.json({
      success: true,
      body: insertResult,
      error_msg: null
    })

  } catch (error: unknown) {
    console.error('Error in photos POST API:', error)
    return NextResponse.json(
      { 
        success: false,
        body: null,
        error_msg: error instanceof Error ? error.message : 'Internal server error'
      },
      { status: 500 }
    )
  }
}

// PUT - Update existing photo
export async function PUT(request: NextRequest) {
  try {
    // Verify authentication
    const { authenticated, authId, clientId } = await verifyAuth()
    if (!authenticated || !authId) {
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Unauthorized'
      }, { status: 401 })
    }

    // Validate clientId exists
    if (!clientId) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Client ID not found in authentication'
      }, { status: 400 })
    }

    // Get the secure webhook URL from environment
    const webhookUrl = process.env.CHHLAT_DB_WEBHOOK_URL
    
    if (!webhookUrl) {
      console.warn('Secure webhook URL not configured')
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Database webhook not configured'
      }, { status: 500 })
    }

    // Parse request body
    const body = await request.json()
    const { photo_id, photo_url, photo_file_path } = body

    if (!photo_id || !photo_url || !photo_file_path) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Missing required fields: photo_id, photo_url, photo_file_path'
      }, { status: 400 })
    }

    // Ensure photo_id is uppercase
    const capitalizedPhotoId = photo_id.toUpperCase()

    // Get knowledge cache data for later cache update
    const knowledgeCacheKey = `knowledge_${authId}`
    const knowledgeData = serverCache.get(knowledgeCacheKey)

    // Generate JWT token for webhook authentication
    const jwtToken = generateWebhookToken()

    // Update photo in PostgreSQL database via webhook (updated_at auto-handled by trigger)
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${jwtToken}`
      },
      body: JSON.stringify({
        operation: 'UPDATE',
        sql: `
          UPDATE photos 
          SET photo_url = $1, photo_file_path = $2 
          WHERE client_id = $3 AND photo_id = $4;
          
          UPDATE faqs 
          SET photo_url = $1, fb_photo_atmid = NULL, tg_photo_atmid = NULL, ig_photo_atmid = NULL
          WHERE client_id = $3 AND photo_id = $4;
        `,
        params: [
          photo_url,
          photo_file_path,
          clientId,
          capitalizedPhotoId
        ]
      })
    })

    if (!response.ok) {
      throw new Error(`Webhook request failed: ${response.status} ${response.statusText}`)
    }

    let webhookData = await response.json()
    
    // Handle array response format from N8N webhook
    if (Array.isArray(webhookData) && webhookData.length > 0) {
      webhookData = webhookData[0]
    }
    
    if (!webhookData.success) {
      throw new Error(webhookData.error_msg || 'Database operation failed')
    }

    // Update knowledge cache after successful DB update (only if cache exists)
    if (knowledgeData && knowledgeData.photos) {
      // Update existing photo in photos array
      const updatedPhotos = knowledgeData.photos.map((photo: any) => 
        photo.photo_id === capitalizedPhotoId 
          ? {
              ...photo,
              photo_url: photo_url,
              photo_file_path: photo_file_path,
              updated_at: new Date().toISOString()
            }
          : photo
      )
      
      knowledgeData.photos = updatedPhotos
      serverCache.set(knowledgeCacheKey, knowledgeData, 30)
    }

    return NextResponse.json({
      success: true,
      body: null,
      error_msg: null
    })

  } catch (error: unknown) {
    console.error('Error in photos PUT API:', error)
    return NextResponse.json(
      { 
        success: false,
        body: null,
        error_msg: error instanceof Error ? error.message : 'Internal server error'
      },
      { status: 500 }
    )
  }
}

// DELETE - Complete photo deletion with dependency check, R2 cleanup, and DB deletion
export async function DELETE(request: NextRequest) {
  try {
    // Verify authentication
    const { authenticated, authId, clientId } = await verifyAuth()
    if (!authenticated || !authId) {
      return NextResponse.json({ 
        success: false,
        dependency: false,
        body: { status: 'error' },
        error_msg: 'Unauthorized'
      }, { status: 401 })
    }

    // Validate clientId exists
    if (!clientId) {
      return NextResponse.json({
        success: false,
        dependency: false,
        body: { status: 'error' },
        error_msg: 'Client ID not found in authentication'
      }, { status: 400 })
    }

    // Get the secure webhook URL from environment
    const webhookUrl = process.env.CHHLAT_DB_WEBHOOK_URL
    
    if (!webhookUrl) {
      console.warn('Secure webhook URL not configured')
      return NextResponse.json({ 
        success: false,
        dependency: false,
        body: { status: 'error' },
        error_msg: 'Database webhook not configured'
      }, { status: 500 })
    }

    // Parse request body
    const body = await request.json()
    const { photo_id, file_paths } = body

    if (!photo_id) {
      return NextResponse.json({
        success: false,
        dependency: false,
        body: { status: 'error' },
        error_msg: 'Missing required parameter: photo_id'
      }, { status: 400 })
    }

    if (!file_paths || !Array.isArray(file_paths)) {
      return NextResponse.json({
        success: false,
        dependency: false,
        body: { status: 'error' },
        error_msg: 'Missing or invalid file_paths array'
      }, { status: 400 })
    }

    // Security validation: Ensure all file paths belong to current authenticated user
    const userPhotoPrefix = `photos/${authId}/`
    const invalidPaths = file_paths.filter(path => !path.startsWith(userPhotoPrefix))
    
    if (invalidPaths.length > 0) {
      console.error(`Unauthorized file access attempt: ${invalidPaths.join(', ')} for auth ${authId}`)
      return NextResponse.json({
        success: false,
        dependency: false,
        body: { status: 'error' },
        error_msg: 'Unauthorized: Invalid file paths'
      }, { status: 403 })
    }

    // Get knowledge cache data for later cache update
    const knowledgeCacheKey = `knowledge_${authId}`
    const knowledgeData = serverCache.get(knowledgeCacheKey)

    // Generate JWT token for webhook authentication
    const jwtToken = generateWebhookToken()

    // Send complete delete request to webhook
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${jwtToken}`
      },
      body: JSON.stringify({
        mode: 'photo',
        operation: 'delete_photo',
        sql_1: `
          SELECT id, question_p, question
          FROM faqs 
          WHERE client_id = $1 AND is_visible = true AND photo_id = $2
        `,
        sql_2: `
          DELETE FROM photos 
          WHERE client_id = $1 AND photo_id = $2
        `,
        params: [clientId, photo_id],
        bucket: 'file',
        file_paths: file_paths
      })
    })

    if (!response.ok) {
      throw new Error(`Webhook request failed: ${response.status} ${response.statusText}`)
    }

    let webhookData = await response.json()
    
    // Handle array response format from N8N webhook
    if (Array.isArray(webhookData) && webhookData.length > 0) {
      webhookData = webhookData[0]
    }

    // Handle successful deletion - update cache
    if (webhookData.success) {
      // Update knowledge cache after successful deletion (only if cache exists)
      if (knowledgeData && knowledgeData.photos) {
        // Remove deleted photo from photos array
        const filteredPhotos = knowledgeData.photos.filter((photo: any) => photo.photo_id !== photo_id)
        const newPhotoCount = filteredPhotos.length
        const photoLimit = knowledgeData.knowledgeStats.photoLimit || 1
        
        // Update knowledge cache with filtered photos and updated stats
        knowledgeData.photos = filteredPhotos
        knowledgeData.knowledgeStats.photoCount = newPhotoCount
        knowledgeData.knowledgeStats.photoUsagePercentage = Math.round((newPhotoCount / photoLimit) * 100)
        
        serverCache.set(knowledgeCacheKey, knowledgeData, 30)
      }
    }

    // Return webhook response as-is (keep body as array for dependency cases)
    return NextResponse.json({
      success: webhookData.success,
      dependency: webhookData.dependency || false,
      body: webhookData.body || { status: 'error' },
      error_msg: webhookData.error_msg || null
    })

  } catch (error: unknown) {
    console.error('Error in photos complete delete API:', error)
    return NextResponse.json(
      { 
        success: false,
        dependency: false,
        body: { status: 'error' },
        error_msg: error instanceof Error ? error.message : 'Internal server error'
      },
      { status: 500 }
    )
  }
}