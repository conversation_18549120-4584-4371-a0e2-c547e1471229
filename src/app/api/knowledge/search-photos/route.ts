import { NextResponse } from 'next/server'
import { verifyAuth } from '@/utils/auth'
import { generateWebhookToken } from '@/utils/jwt'

export async function POST(request: Request) {
  try {
    // Verify authentication
    const { authenticated, authId } = await verifyAuth()
    if (!authenticated || !authId) {
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Unauthorized'
      }, { status: 401 })
    }

    // Get the secure webhook URL from environment
    const webhookUrl = process.env.CHHLAT_DB_WEBHOOK_URL
    
    if (!webhookUrl) {
      console.warn('Secure webhook URL not configured')
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Database webhook not configured'
      }, { status: 500 })
    }

    // Generate JWT token for webhook authentication
    const jwtToken = generateWebhookToken()

    // Fetch all photos using same SQL as knowledge cache (safety fallback)
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${jwtToken}`
      },
      body: JSON.stringify({
        operation: 'SELECT',
        sql: `
          SELECT COALESCE(
            JSON_AGG(
              JSON_BUILD_OBJECT(
                'id', id,
                'photo_id', photo_id,
                'photo_url', photo_url,
                'photo_file_path', photo_file_path,
                'updated_at', updated_at
              ) ORDER BY updated_at DESC
            ), 
            '[]'::json
          ) as photos
          FROM photos 
          WHERE client_id = (SELECT client_id FROM clients WHERE auth_id = $1)
        `,
        params: [authId]
      })
    })

    if (!response.ok) {
      throw new Error(`Webhook request failed: ${response.status} ${response.statusText}`)
    }

    let webhookData = await response.json()
    
    // Handle array response format from N8N webhook
    if (Array.isArray(webhookData) && webhookData.length > 0) {
      webhookData = webhookData[0]
    }
    
    if (!webhookData.success) {
      throw new Error(webhookData.error_msg || 'Database operation failed')
    }

    let result = webhookData.body
    
    // Handle double array wrapper for search photos response
    if (Array.isArray(result) && result.length > 0) {
      result = result[0]
    }
    
    // Extract photos from the result (should be the photos JSON array)
    const photos = result?.photos || []

    // Return standardized response format
    return NextResponse.json({
      success: true,
      body: photos,
      error_msg: null
    })

  } catch (error: unknown) {
    console.error('Error in photo search API:', error)
    return NextResponse.json(
      { 
        success: false,
        body: null,
        error_msg: error instanceof Error ? error.message : 'Internal server error'
      },
      { status: 500 }
    )
  }
}