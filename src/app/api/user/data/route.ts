import { NextRequest, NextResponse } from 'next/server'
import { verifyAuth } from '@/utils/auth'
import { generateWebhookToken } from '@/utils/jwt'
import { serverCache } from '@/lib/cache'

export async function GET(request: NextRequest) {
  try {
    // Verify authentication
    const { authenticated, authId, clientId } = await verifyAuth()
    if (!authenticated || !authId) {
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Unauthorized'
      }, { status: 401 })
    }

    // Get cache type from query parameter
    const { searchParams } = new URL(request.url)
    const cacheType = searchParams.get('cache') || 'dashboard'

    if (!['dashboard', 'knowledge'].includes(cacheType)) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Invalid cache type. Must be "dashboard" or "knowledge"'
      }, { status: 400 })
    }

    // Check cache FIRST using auth_id
    const cacheKey = `${cacheType}_${authId}`
    const cachedResult = serverCache.get(cacheKey)
    if (cachedResult) {
      return NextResponse.json({
        success: true,
        body: cachedResult,
        error_msg: null,
        cached: true
      })
    }

    // Get the secure webhook URL from environment
    const webhookUrl = process.env.CHHLAT_DB_WEBHOOK_URL
    
    if (!webhookUrl) {
      console.warn('Secure webhook URL not configured')
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Database webhook not configured'
      }, { status: 500 })
    }

    // Generate JWT token for webhook authentication
    const jwtToken = generateWebhookToken()

    let sql: string
    let responseData: any

    if (cacheType === 'dashboard') {
      // Dashboard cache SQL - include client_id and usage data only
      sql = `
        SELECT 
          c.client_id,
          c.username,
          c.sector,
          c.lang,
          c.plan_type,
          c.next_billing_date,
          c.usage_used,
          c.usage_limit
        FROM clients c
        WHERE c.auth_id = $1
      `
    } else {
      // Knowledge cache SQL - include sector and other data
      sql = `
        SELECT 
          c.client_id,
          c.sector,
          c.lang as client_lang,
          COALESCE(p.total_faqs, 0) as faq_limit,
          COALESCE(p.total_photos, 0) as photo_limit,
          (SELECT COUNT(*) FROM faqs WHERE client_id = c.client_id AND is_visible = true) as faq_count,
          (
            SELECT COALESCE(
              JSON_AGG(
                JSON_BUILD_OBJECT(
                  'id', id,
                  'photo_id', photo_id,
                  'photo_url', photo_url,
                  'photo_file_path', photo_file_path,
                  'updated_at', updated_at
                ) ORDER BY updated_at DESC
              ), 
              '[]'::json
            )
            FROM photos 
            WHERE client_id = c.client_id
          ) as photos
        FROM clients c
        LEFT JOIN plans p ON p.name = c.plan_type
        WHERE c.auth_id = $1
      `
    }

    // Send request to N8N webhook
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${jwtToken}`
      },
      body: JSON.stringify({
        operation: 'SELECT',
        sql: sql,
        params: [authId]
      })
    })

    if (!response.ok) {
      throw new Error(`Webhook request failed: ${response.status} ${response.statusText}`)
    }

    let webhookData = await response.json()
    
    // Handle array response format from N8N webhook
    if (Array.isArray(webhookData) && webhookData.length > 0) {
      webhookData = webhookData[0]
    }
    
    // Check webhook success/failure
    if (!webhookData.success) {
      throw new Error(webhookData.error_msg || 'Database operation failed')
    }

    // Get data from webhook body
    let row = webhookData.body
    
    // Handle double array wrapper for both dashboard and knowledge cache responses
    if (Array.isArray(row) && row.length > 0) {
      row = row[0]
    }
    
    if (!row || !row.client_id) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: `No ${cacheType} data found for user`
      }, { status: 404 })
    }


    if (cacheType === 'dashboard') {
      // Format dashboard cache response
      responseData = {
        clientInfo: {
          username: row.username,
          sector: row.sector,
          lang: row.lang,
          plan_type: row.plan_type,
          next_billing_date: row.next_billing_date
        },
        usageData: {
          usage_used: row.usage_used || 0,
          usage_limit: row.usage_limit || 100
        }
      }
    } else {
      // Format knowledge cache response
      const photos = Array.isArray(row.photos) ? row.photos : []
      const photoCount = photos.length
      const faqUsagePercentage = row.faq_limit > 0 ? Math.min((row.faq_count / row.faq_limit) * 100, 100) : 0
      const photoUsagePercentage = row.photo_limit > 0 ? Math.min((photoCount / row.photo_limit) * 100, 100) : 0

      responseData = {
        sector: row.sector,
        clientLang: row.client_lang,
        knowledgeStats: {
          faqCount: parseInt(row.faq_count) || 0,
          photoCount: photoCount,
          faqLimit: parseInt(row.faq_limit) || 0,
          photoLimit: parseInt(row.photo_limit) || 0,
          faqUsagePercentage: Math.round(faqUsagePercentage),
          photoUsagePercentage: Math.round(photoUsagePercentage)
        },
        photos: photos
      }
    }

    // Cache the result for 30 minutes
    serverCache.set(cacheKey, responseData, 30)

    return NextResponse.json({
      success: true,
      body: responseData,
      error_msg: null
    })
  } catch (error) {
    console.error('Error in user data API:', error)
    return NextResponse.json(
      { 
        success: false,
        body: null,
        error_msg: error instanceof Error ? error.message : 'Internal server error'
      },
      { status: 500 }
    )
  }
}