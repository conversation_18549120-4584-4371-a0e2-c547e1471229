'use client'

import Link from 'next/link'
import { motion } from 'framer-motion'
import { useState, useEffect, useMemo, useCallback } from 'react'
import { FiSettings } from 'react-icons/fi'
import { FaPaperPlane, FaGlobe } from 'react-icons/fa'
import { useLanguage } from '@/context/LanguageContext'
import { useTheme, useThemeConfig } from '@/context/ThemeContext'
import DashboardLanguageSwitcher from '@/components/DashboardLanguageSwitcher'
import ThemeAwareSkeleton from '@/components/ThemeAwareSkeleton'
import DashboardFooter from '@/components/DashboardFooter'
import { useDashboardData } from '@/hooks/useOptimizedData'


export default function Dashboard() {
  const [dismissedWarnings, setDismissedWarnings] = useState({ billing: false, usage: false })
  const { t, language } = useLanguage()
  const { theme, toggleTheme } = useTheme()
  const themeConfig = useThemeConfig()

  // Use optimized dashboard data hook - this is now our single source of truth
  const { data: dashboardData, loading: isDataLoading, error: dashboardError } = useDashboardData()

  // Extract client info from dashboard data (no separate cache needed)
  const clientInfo = dashboardData?.clientInfo || null

  // Extract data from optimized hook (simple structure) - memoized
  const subscriptionData = useMemo(() => {
    return dashboardData?.clientInfo ? {
      plan_type: language === 'km' ?
        (dashboardData.clientInfo.plan_type === 'Intern' ? 'អ្នកហាត់ការ' :
         dashboardData.clientInfo.plan_type === 'Assistant' ? 'ជំនួយការ' :
         dashboardData.clientInfo.plan_type) : dashboardData.clientInfo.plan_type,
      next_billing_date: dashboardData.clientInfo.next_billing_date
    } : { plan_type: null, next_billing_date: null }
  }, [dashboardData?.clientInfo, language])

  const usageData = dashboardData?.usageData || { usage_used: 0, usage_limit: 100 }


  // Removed automatic sync between UI language and bot language
  // UI language (browser) and bot language (database) are completely separate systems

  // Authentication is handled by server-side layout protection

  // Format the date to a more readable format - memoized
  const formattedBillingDate = useMemo(() => {
    const formatDate = (dateString: string | null) => {
      if (!dateString) return 'Not available';
      try {
        // Convert UTC date to local timezone for proper comparison
        const utcDate = new Date(dateString);
        // Convert to local date string (YYYY-MM-DD format)
        const localBillingDate = utcDate.toLocaleDateString('en-CA', { timeZone: 'Asia/Phnom_Penh' });

        // Format for display
        const displayDate = new Date(localBillingDate);
        return displayDate.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        });
      } catch (error) {
        console.error('Error formatting date:', error);
        return 'Invalid date';
      }
    };
    return formatDate(subscriptionData.next_billing_date);
  }, [subscriptionData.next_billing_date]);

  // Check if billing date is overdue
  const isBillingOverdue = () => {
    if (!subscriptionData.next_billing_date) return false;
    
    // OLD LOGIC (commented out)
    // const billingDate = new Date(subscriptionData.next_billing_date);
    // // Get current date in Asia/Phnom_Penh timezone
    // const today = new Date();
    // const todayInPhnomPenh = new Date(today.toLocaleString('en-US', { timeZone: 'Asia/Phnom_Penh' }));
    // todayInPhnomPenh.setHours(0, 0, 0, 0); // Reset time to start of day for accurate comparison
    // return billingDate < todayInPhnomPenh;
    
    // NEW LOGIC - simplified ISO date string comparison
    const today = new Date().toISOString().split('T')[0]; 
    // today = "2025-07-12"
    
    const billingDate = subscriptionData.next_billing_date.split('T')[0]; 
    // billingDate = "2025-08-12" 
    
    return today > billingDate;
    // "2025-07-12" > "2025-08-12" = false ✅
  };

  // Check if usage limit is reached - memoized
  const isUsageLimitReached = useMemo(() => {
    return usageData.usage_used >= usageData.usage_limit;
  }, [usageData.usage_used, usageData.usage_limit]);

  // Memoize progress bar width calculation
  const progressWidth = useMemo(() => {
    return Math.min((usageData.usage_used / usageData.usage_limit) * 100, 100);
  }, [usageData.usage_used, usageData.usage_limit]);

  // Optimized warning dismiss handler
  const handleDismissWarning = useCallback((type: 'billing' | 'usage') => {
    setDismissedWarnings(prev => ({ ...prev, [type]: true }));
  }, []);

  return (
    <div className={themeConfig.pageBackground}>
      {/* Background effects (only for dark theme) */}
      {themeConfig.backgroundEffects}
      {/* Theme-aware Header */}
      <motion.header
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="relative"
      >
        <div className="container mx-auto px-3 py-3">
          <div
            className={`relative ${themeConfig.card} rounded-2xl px-4 py-3 border ${themeConfig.border} transition-all duration-300 overflow-hidden`}
            style={theme === 'dark' ? {
              boxShadow: '0 0 10px rgba(255, 255, 255, 0.08), inset 0 0 15px rgba(255, 255, 255, 0.15)'
            } : {
              boxShadow: '0 0 8px rgba(0, 0, 0, 0.04), inset 0 0 12px rgba(0, 0, 0, 0.06)'
            }}
          >

            {/* Content */}
            <div className="relative z-10 flex justify-between items-center">
              <Link href="/dashboard" className="group">
                <img
                  src={themeConfig.logo}
                  alt="Chhlat Bot"
                  className="h-8 w-auto transition-transform duration-300 group-hover:scale-105"
                />
              </Link>

              <div className="flex items-center space-x-2 sm:space-x-3">
                {/* User Email - Desktop with icon */}
                <div className="hidden md:flex items-center space-x-2">
                  <div className={`w-7 h-7 ${themeConfig.card} border ${themeConfig.border} rounded-full flex items-center justify-center`}>
                    <svg className="w-3.5 h-3.5 text-jade-purple" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                  <div className={`text-sm ${themeConfig.textSecondary} font-body`}>
                    {dashboardData?.clientInfo?.username}
                  </div>
                </div>

                {/* User Email - Mobile/Tablet without icon */}
                <div className={`flex md:hidden text-xs ${themeConfig.textSecondary} font-body`}>
                  {dashboardData?.clientInfo?.username}
                </div>

                {/* Language Switcher */}
                <div>
                  <DashboardLanguageSwitcher />
                </div>

                {/* Theme Toggle Button */}
                <button
                  onClick={toggleTheme}
                  className={`w-8 h-8 sm:w-9 sm:h-9 ${themeConfig.interactiveDark} border ${themeConfig.border} rounded-lg flex items-center justify-center ${themeConfig.text} ${themeConfig.interactiveHover} ${themeConfig.borderHover} transition-all duration-300 group`}
                >
                  {theme === 'dark' ? (
                    <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                  ) : (
                    <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                    </svg>
                  )}
                </button>

                {/* Settings Button */}
                <Link
                  href="/dashboard/settings"
                  className={`w-8 h-8 sm:w-9 sm:h-9 ${themeConfig.interactiveDark} border ${themeConfig.border} rounded-lg flex items-center justify-center ${themeConfig.text} ${themeConfig.interactiveHover} ${themeConfig.borderHover} transition-all duration-300 group`}
                >
                  <FiSettings size={16} className="transition-transform duration-300 group-hover:rotate-90" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </motion.header>

      {/* Dashboard Content */}
      <div className="flex-grow container mx-auto px-4 py-8">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          {/* Error handling for dashboard data */}
          {dashboardError && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className={`${themeConfig.errorBackground} backdrop-blur-md border ${themeConfig.errorBorder} rounded-xl p-4 mb-8 ${themeConfig.errorText} ${themeConfig.errorShadow}`}
            >
              <h2 className="text-xl font-semibold mb-2">Error Loading Dashboard</h2>
              <p className="mb-4">Unable to load dashboard data. Please try refreshing the page.</p>
              <p className="text-sm">{dashboardError}</p>
            </motion.div>
          )}

          {!clientInfo && !isDataLoading && !dashboardError && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className={`${themeConfig.warningBackground} backdrop-blur-md border ${themeConfig.warningBorder} rounded-xl p-4 mb-8 ${themeConfig.warningText} ${themeConfig.warningShadow}`}
            >
              <h2 className="text-xl font-semibold mb-2">{t('account_setup_incomplete')}</h2>
              <p className="mb-4">{t('account_setup_message')}</p>
              <p>{t('email')} {dashboardData?.clientInfo?.username}</p>
              <p className="text-sm mt-2">{t('contact_support')}</p>
            </motion.div>
          )}

          {/* Warning Messages */}
          {(isBillingOverdue() || isUsageLimitReached) && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.3 }}
              className="mb-6"
            >
              {isBillingOverdue() && !dismissedWarnings.billing && (
                <div className={`${themeConfig.errorBackground} backdrop-blur-md border ${themeConfig.errorBorder} rounded-xl p-4 mb-4 ${themeConfig.errorText} ${themeConfig.errorShadow} relative`}>
                  <button
                    onClick={() => handleDismissWarning('billing')}
                    className="absolute top-3 right-3 w-6 h-6 bg-red-500/20 hover:bg-red-500/30 rounded-full flex items-center justify-center text-red-400 hover:text-red-300 transition-all duration-200"
                  >
                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                  <div className="flex items-center space-x-3 pr-8">
                    <div className="w-8 h-8 bg-red-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                      <svg className="w-4 h-4 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                      </svg>
                    </div>
                    <p className="text-sm font-body">{t('plan_expired_warning')}</p>
                  </div>
                </div>
              )}

              {isUsageLimitReached && !dismissedWarnings.usage && (
                <div className={`${themeConfig.usageWarningBackground} backdrop-blur-md border ${themeConfig.usageWarningBorder} rounded-xl p-4 mb-4 ${themeConfig.usageWarningText} ${themeConfig.usageWarningShadow} relative`}>
                  <button
                    onClick={() => handleDismissWarning('usage')}
                    className="absolute top-3 right-3 w-6 h-6 bg-orange-500/20 hover:bg-orange-500/30 rounded-full flex items-center justify-center text-orange-400 hover:text-orange-300 transition-all duration-200"
                  >
                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                  <div className="flex items-center space-x-3 pr-8">
                    <div className="w-8 h-8 bg-orange-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                      <svg className="w-4 h-4 text-orange-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <p className="text-sm font-body">{t('usage_limit_warning')}</p>
                  </div>
                </div>
              )}
            </motion.div>
          )}

          {/* Main Dashboard Content */}
          {isDataLoading ? (
            <ThemeAwareSkeleton />
          ) : (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <div className="mb-6">
                {/* Combined Messages and Plan Card */}
                <div className="mb-6">
                  {/* Combined Messages & Plan Card */}
                  <div 
                    className={`relative ${themeConfig.card} rounded-2xl p-6 border ${themeConfig.border} transition-all duration-300 group overflow-hidden`}
                    style={theme === 'dark' ? {
                      boxShadow: '0 0 10px rgba(255, 255, 255, 0.08), inset 0 0 15px rgba(255, 255, 255, 0.15)'
                    } : {
                      boxShadow: '0 0 8px rgba(0, 0, 0, 0.04), inset 0 0 12px rgba(0, 0, 0, 0.06)'
                    }}
                  >
                    <div className="relative z-10">
                      {/* Header */}
                      <div className="flex items-center justify-between mb-6">
                        <h2 className={`text-xl font-extrabold ${themeConfig.text} font-title`}>{t('dashboard_header')}</h2>
                        <a
                          href="https://t.me/chhlatbot"
                          target="_blank"
                          rel="noopener noreferrer"
                          className="px-4 py-2 bg-gradient-to-r from-dashboard-primary to-purple-600 text-white rounded-xl font-body text-sm hover:from-dashboard-primary/80 hover:to-purple-600/80 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105"
                        >
                          {t('upgrade')}
                        </a>
                      </div>

                      {/* Content Grid */}
                      <div className="grid grid-cols-2 md:grid-cols-2 gap-6 md:gap-20">
                        {/* Messages Section  */}
                        <div className="space-y-4">
                          <div className="flex items-center space-x-3">
                            <div className={`w-10 h-10 ${theme === 'dark' ? themeConfig.secondCard : 'bg-jade-purple-light'} rounded-xl border-2 ${themeConfig.border} flex items-center justify-center`}>
                              <FaPaperPlane className={themeConfig.text} size={18} />
                            </div>
                            <div>
                              <h3 className={`text-lg font-extrabold ${themeConfig.text} font-title`}>{t('messages')}</h3>
                              {/* <p className="text-xs text-zinc-400 font-body">{t('usage')} {t('progress')}</p> */}
                            </div>
                          </div>

                          <div className="space-y-3">
                            <div className="flex justify-between items-end">
                              <div>
                                <div className="text-2xl font-bold text-jade-purple font-title">
                                  {usageData.usage_used.toLocaleString()}
                                </div>
                                <div className={`text-sm ${themeConfig.textMuted} font-body`}>
                                  of {usageData.usage_limit.toLocaleString()} {t('total')}
                                </div>
                              </div>
                              <div className="text-right">
                                <div className={`text-lg font-bold ${themeConfig.textSecondary} font-title`}>
                                  {Math.round(progressWidth)}%
                                </div>
                                <div className={`text-xs ${themeConfig.textMuted} font-body`}>used</div>
                              </div>
                            </div>

                            <div className={`h-3 ${themeConfig.secondCard} rounded-full overflow-hidden`}>
                              <div
                                className={`h-full bg-gradient-to-r from-dashboard-primary via-purple-500 to-dashboard-info rounded-full ${isDataLoading ? 'transition-all duration-500' : 'transition-all duration-200'}`}
                                style={{
                                  width: `${progressWidth}%`
                                }}
                              ></div>
                            </div>
                          </div>
                        </div>

                        {/* Plan Section */}
                        <div className="space-y-4">
                          <div className="flex items-center space-x-3">
                            <div className={`w-10 h-10 ${theme === 'dark' ? themeConfig.secondCard : 'bg-jade-purple-light'} rounded-xl border-2 ${themeConfig.border} flex items-center justify-center`}>
                              <svg className={`w-5 h-5 ${themeConfig.text}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                              </svg>
                            </div>
                            <div>
                              <h3 className={`text-lg font-extrabold ${themeConfig.text} font-title`}>{t('plan')}</h3>
                              {/* <p className="text-xs text-zinc-400 font-body">{t('current')} subscription</p> */}
                            </div>
                          </div>

                          <div className="space-y-3">
                            <div>
                              <div className="text-xl font-bold text-jade-purple font-title py-3">
                                {subscriptionData.plan_type || 'N/A'}
                              </div>
                              <div className={`text-sm ${themeConfig.textMuted} font-body`}>
                                {t('bill')} {formattedBillingDate}
                              </div>
                            </div>

                            {/* <Link
                              href="/dashboard/plans"
                              className="inline-flex items-center space-x-2 text-sm text-jade-purple hover:text-white transition-colors font-body"
                            >
                              <span>Manage Plan</span>
                              <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                              </svg>
                            </Link> */}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Secondary Cards Row */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                {/* AI Brain Card */}
                <Link
                  href="/dashboard/knowledge"
                  className={`relative ${themeConfig.card} rounded-2xl p-6 border ${themeConfig.border} ${themeConfig.borderHover} transition-all duration-300 group overflow-hidden`}
                  style={theme === 'dark' ? {
                    boxShadow: '0 0 10px rgba(255, 255, 255, 0.08), inset 0 0 15px rgba(255, 255, 255, 0.15)'
                  } : {
                    boxShadow: '0 0 8px rgba(0, 0, 0, 0.04), inset 0 0 12px rgba(0, 0, 0, 0.06)'
                  }}
                >
                  <div className="relative z-10">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <div className={`w-10 h-10 ${theme === 'dark' ? themeConfig.secondCard : 'bg-jade-purple-light'} rounded-xl border-2 ${themeConfig.border} flex items-center justify-center`}>
                          <svg className={`w-5 h-5 ${themeConfig.text}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                          </svg>
                        </div>
                        <div>
                          <h3 className={`text-lg font-extrabold ${themeConfig.text} font-title`}>{t('ai_brain')}</h3>
                          <p className={`text-xs ${themeConfig.textMuted} font-body`}>{t('knowledgebase')}</p>
                        </div>
                      </div>
                    </div>
                    <div className="space-y-3">
                      <p className={`text-sm ${themeConfig.textSecondary} font-body`}>{t('train_your_chhlatbot')}</p>
                      <div className="flex flex-wrap gap-2">
                        <span className={`text-xs ${themeConfig.interactiveDark} ${themeConfig.textMuted} px-3 py-1.5 rounded-full font-body border ${themeConfig.border} flex items-center space-x-1`}>
                          <svg className="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h7" />
                          </svg>
                          <span>{t('text')}</span>
                        </span>
                        <span className={`text-xs ${themeConfig.interactiveDark} ${themeConfig.textMuted} px-3 py-1.5 rounded-full font-body border ${themeConfig.border} flex items-center space-x-1`}>
                          <svg className="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                          </svg>
                          <span>{t('voice')}</span>
                        </span>
                        <span className={`text-xs ${themeConfig.interactiveDark} ${themeConfig.textMuted} px-3 py-1.5 rounded-full font-body border ${themeConfig.border} flex items-center space-x-1`}>
                          <svg className="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                          <span>{t('image')}</span>
                        </span>
                      </div>
                    </div>
                  </div>
                </Link>

                {/* Connections Card */}
                <Link
                  href="/dashboard/connect"
                  className={`relative ${themeConfig.card} rounded-2xl p-6 border ${themeConfig.border} ${themeConfig.borderHover} transition-all duration-300 group overflow-hidden`}
                  style={theme === 'dark' ? {
                    boxShadow: '0 0 10px rgba(255, 255, 255, 0.08), inset 0 0 15px rgba(255, 255, 255, 0.15)'
                  } : {
                    boxShadow: '0 0 8px rgba(0, 0, 0, 0.04), inset 0 0 12px rgba(0, 0, 0, 0.06)'
                  }}
                >
                  <div className="relative z-10">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <div className={`w-10 h-10 ${theme === 'dark' ? themeConfig.secondCard : 'bg-jade-purple-light'} rounded-xl flex border-2 ${themeConfig.border} items-center justify-center`}>
                          <svg className={`w-5 h-5 ${themeConfig.text}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                          </svg>
                        </div>
                        <div>
                          <h3 className={`text-lg font-extrabold ${themeConfig.text} font-title`}>{t('connects')}</h3>
                          <p className={`text-xs ${themeConfig.textMuted} font-body`}>{t('socialchannels')}</p>
                        </div>
                      </div>
                    </div>
                    <div className="space-y-3">
                      <p className={`text-sm ${themeConfig.textSecondary} font-body`}>{t('connect_your_social_platforms')}</p>
                      <div className="flex flex-wrap gap-3">
                        {/* Facebook Messenger */}
                        <div className={`w-10 h-10 ${themeConfig.interactiveDark} rounded-xl flex items-center justify-center ${themeConfig.textSecondary} border ${themeConfig.border}`}>
                          <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 2C6.477 2 2 6.145 2 11.259C2 14.128 3.134 16.71 5 18.552V22.5L8.801 20.509C9.795 20.83 10.871 21 12 21C17.523 21 22 16.855 22 11.741C22 6.627 17.523 2.482 12 2ZM13.162 14.841L10.4 11.841L5 14.841L10.8 8.759L13.6 11.759L19 8.759L13.162 14.841Z" fill="currentColor"/>
                          </svg>
                        </div>
                        {/* Instagram */}
                        <div className={`w-10 h-10 ${themeConfig.interactiveDark} rounded-xl flex items-center justify-center ${themeConfig.textSecondary} border ${themeConfig.border}`}>
                          <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect x="2" y="2" width="20" height="20" rx="5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                            <circle cx="12" cy="12" r="4" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                            <path d="M18 6L18 6.01" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                        </div>
                        {/* Telegram */}
                        <div className={`w-10 h-10 ${themeConfig.interactiveDark} rounded-xl flex items-center justify-center ${themeConfig.textSecondary} border ${themeConfig.border}`}>
                          <FaPaperPlane size={16} />
                        </div>
                        {/* Web */}
                        <div className={`w-10 h-10 ${themeConfig.interactiveDark} rounded-xl flex items-center justify-center ${themeConfig.textSecondary} border ${themeConfig.border}`}>
                          <FaGlobe size={16} />
                        </div>
                      </div>
                    </div>
                  </div>
                </Link>
              </div>
            </motion.div>
          )}
        </motion.div>
      </div>
      <DashboardFooter />
    </div>
  )
}
