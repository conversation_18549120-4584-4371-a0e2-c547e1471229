'use client'

import { motion } from 'framer-motion'
import { useState, useEffect } from 'react'
import { useAuth } from '@/context/AuthContext'
import { useLanguage } from '@/context/LanguageContext'
import Link from 'next/link'
import GlassButton from './ui/GlassButton'

export default function CallToAction() {
  const [isVisible, setIsVisible] = useState(false)
  const { user } = useAuth()
  const { t } = useLanguage()

  useEffect(() => {
    setIsVisible(true)
  }, [])

  return (
    <section className="py-24 relative overflow-hidden">
      {/* <div className="absolute inset-0 opacity-10">
        <div className="grid grid-cols-12 gap-4 h-full">
          {Array.from({ length: 24 }).map((_, i) => (
            <div key={i} className="h-full w-px bg-white opacity-10" style={{ left: `${(i / 24) * 100}%` }}></div>
          ))}
          {Array.from({ length: 12 }).map((_, i) => (
            <div key={i} className="h-px w-full bg-white opacity-10" style={{ top: `${(i / 12) * 100}%` }}></div>
          ))}
        </div>
      </div> */}

      <div className="container mx-auto px-4 relative z-10">
        <div className="flex flex-col gap-12">
          <motion.div
            className="text-white text-center"
            initial={{ opacity: 0, y: -20 }}
            animate={isVisible ? { opacity: 1, y: 0 } : { opacity: 0, y: -20 }}
            transition={{ duration: 0.5 }}
          >
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-6 font-['GeistSans','GeistSans_Fallback',sans-serif]">
              {t('cta_title_part1')} <span className="text-jade-purple">{t('cta_title_part2')}</span> {t('cta_title_part3')}
            </h2>
            <p className="text-base sm:text-lg md:text-xl mb-8 font-body text-jade-gray max-w-3xl mx-auto">
              {t('cta_subtitle')}
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              {user ? (
                <Link href="/dashboard">
                  <GlassButton className="text-lg" variant="filled">
                    {t('cta_dashboard')}
                  </GlassButton>
                </Link>
              ) : (
                <Link href="/register">
                  <GlassButton className="text-lg" variant="filled">
                    {t('cta_try_free')}
                  </GlassButton>
                </Link>
              )}
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}