'use client'

import { FaMicrophone } from 'react-icons/fa'
import { useTheme, useThemeConfig } from '@/context/ThemeContext'
import { useLanguage } from '@/context/LanguageContext'
import AudioPlayer from './AudioPlayer'

interface AnswerEditFieldProps {
  value: string
  isAudioAnswer: boolean
  audioUrl?: string | null
  audioValidation?: {
    valid: boolean
    duration?: number
    error?: string
  } | null
  isValidating?: boolean
  onClick: () => void
  onAudioToggle: () => void
  disabled?: boolean
  placeholder?: string
  showClickToEdit?: boolean
  showClickToPlay?: boolean
  // Audio playback props for existing audio files
  audioId?: string | number
  isAudioSelected?: boolean
  isAudioPlaying?: boolean
  onAudioClick?: () => void
}

export default function AnswerEditField({
  value,
  isAudioAnswer,
  audioUrl,
  audioValidation,
  isValidating = false,
  onClick,
  onAudioToggle,
  disabled = false,
  placeholder,
  showClickToEdit = true,
  showClickToPlay = true,
  audioId,
  isAudioSelected = false,
  isAudioPlaying = false,
  onAudioClick
}: AnswerEditFieldProps) {
  const { theme } = useTheme()
  const themeConfig = useThemeConfig()
  const { t } = useLanguage()

  // Determine the display content based on priority
  const renderContent = () => {
    // Priority: audioUrl > valid audio code > validating > audio mode > text mode
    if (audioUrl) {
      // State A: Audio file exists - show AudioPlayer
      return (
        <div className="flex items-center min-w-0 pr-12" title={showClickToPlay ? t('click_to_play') : undefined}>
          <AudioPlayer
            audioUrl={audioUrl}
            isSelected={isAudioSelected}
            isPlaying={isAudioPlaying}
            onClick={onAudioClick || (() => {})}
            compact={true}
          />
        </div>
      )
    }

    if (isAudioAnswer && audioValidation?.valid) {
      // State B: Valid audio code - show voice indicator
      return (
        <div className="flex items-center">
          <div className="flex items-center">
            <svg 
              className={`w-4 h-4 mr-2 ${theme === 'dark' ? 'text-white' : 'text-jade-purple-dark'}`}
              fill="currentColor" 
              viewBox="0 0 24 24"
            >
              <rect x="2" y="8" width="2" height="8" rx="1"/>
              <rect x="6" y="5" width="2" height="14" rx="1"/>
              <rect x="10" y="10" width="2" height="4" rx="1"/>
              <rect x="14" y="3" width="2" height="18" rx="1"/>
              <rect x="18" y="7" width="2" height="10" rx="1"/>
              <rect x="22" y="12" width="2" height="2" rx="1"/>
            </svg>
            <span className={`text-sm ${themeConfig.textMuted} flex-shrink-0`}>
              {t('voice')} {audioValidation.duration}s
            </span>
          </div>
        </div>
      )
    }

    if (isValidating) {
      // State C: Validating audio code - show loading
      return (
        <div className="flex items-center">
          <div className="mr-2 w-4 h-4 border-2 border-jade-purple border-t-transparent rounded-full animate-spin flex-shrink-0"></div>
          <span className={`${themeConfig.textMuted} flex-shrink-0`}>
            {t('validating')}...
          </span>
        </div>
      )
    }

    if (isAudioAnswer) {
      // State D: Audio mode, no code - show placeholder
      return (
        <span className="truncate min-w-0">
          {placeholder || t('paste_audio_code')}
        </span>
      )
    }

    // State E: Text mode
    if (value && value.trim()) {
      // Text mode with content - show text
      return (
        <span className="truncate min-w-0">
          {value}
        </span>
      )
    }

    // Text mode, no content - show placeholder
    return (
      <span className={`${themeConfig.textMuted} truncate min-w-0`}>
        {placeholder || t('enter_reply')}
      </span>
    )
  }

  // Determine button state and behavior
  const getButtonState = () => {
    if (audioUrl || (isAudioAnswer && audioValidation?.valid)) {
      return {
        variant: 'delete',
        title: audioUrl ? 'Delete audio file' : 'Clear audio code',
        icon: 'delete'
      }
    }

    if (isAudioAnswer) {
      return {
        variant: 'audio-active',
        title: 'Switch to text mode',
        icon: 'microphone'
      }
    }

    return {
      variant: 'audio-inactive',
      title: 'Switch to audio mode',
      icon: 'microphone'
    }
  }

  const buttonState = getButtonState()

  const getButtonClassName = () => {
    const baseClasses = "absolute right-2 top-1/2 transform -translate-y-1/2 p-1.5 rounded-full transition-all duration-200"
    
    if (disabled) {
      return `${baseClasses} opacity-50 cursor-not-allowed`
    }

    switch (buttonState.variant) {
      case 'delete':
        return `${baseClasses} bg-red-500/20 text-red-500 hover:bg-red-500/30`
      case 'audio-active':
        return `${baseClasses} bg-jade-purple text-white hover:bg-jade-purple/80`
      case 'audio-inactive':
      default:
        return `${baseClasses} ${themeConfig.textMuted} hover:text-jade-purple hover:bg-jade-purple/10`
    }
  }

  const getClickTitle = () => {
    if (disabled) return undefined
    
    if (audioUrl && showClickToPlay) {
      return t('click_to_play')
    }
    
    if (showClickToEdit) {
      return t('click_to_edit')
    }
    
    return undefined
  }

  return (
    <div
      className={`w-full px-3 py-2 ${themeConfig.secondCard} border ${themeConfig.border} rounded-lg ${themeConfig.text} ${
        disabled 
          ? 'cursor-default' 
          : audioUrl 
            ? 'cursor-pointer' 
            : !isAudioAnswer 
              ? `${themeConfig.borderHover} cursor-pointer` 
              : audioValidation?.valid
                ? 'cursor-default'
                : `${themeConfig.borderHover} cursor-pointer`
      } transition-all relative`}
      onClick={disabled || (isAudioAnswer && audioValidation?.valid) ? undefined : onClick}
      title={getClickTitle()}
    >
      <div className={`${!value && !audioUrl && !audioValidation?.valid ? themeConfig.textMuted : ''} flex items-center min-w-0 pr-12`}>
        {renderContent()}
      </div>

      {/* Dynamic Icon Button */}
      <button
        onClick={(e) => {
          e.stopPropagation()
          if (!disabled) {
            onAudioToggle()
          }
        }}
        disabled={disabled}
        className={getButtonClassName()}
        title={disabled ? undefined : buttonState.title}
      >
        {buttonState.icon === 'delete' ? (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
        ) : (
          <FaMicrophone className="w-4 h-4" />
        )}
      </button>
    </div>
  )
}