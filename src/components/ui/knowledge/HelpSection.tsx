'use client'

import { FaYoutube, FaTelegram } from 'react-icons/fa'
import { useLanguage } from '@/context/LanguageContext'
import { useThemeConfig } from '@/context/ThemeContext'

interface HelpSectionProps {
  youtubeUrl?: string
  message?: string
  clientLang?: string
}

export default function HelpSection({ 
  youtubeUrl = '#', 
  message = '',
  clientLang = 'English'
}: HelpSectionProps) {
  const { t } = useLanguage()
  const themeConfig = useThemeConfig()
  

  return (
    <div className="mb-6">
      {/* Description Text Above Buttons */}
      <p className={`${themeConfig.textSecondary} text-sm mb-2 font-body text-center`}>
        {t('help_video_tutorial_description')}
      </p>
      
      {/* Buttons Row */}
      <div className="flex gap-3 sm:justify-center mb-4">
        {/* YouTube Guide Button */}
        <button
          onClick={() => window.open(youtubeUrl, '_blank')}
          className={`flex items-center justify-center space-x-2 px-3 py-2 ${themeConfig.card} border-2 ${themeConfig.border} rounded-lg ${themeConfig.borderHover} transition-all duration-200 hover:scale-105 text-sm flex-1 sm:flex-none sm:w-auto`}
          title="Watch the step-by-step tutorial"
        >
          <FaYoutube className="w-4 h-4 text-red-500" />
          <span className={themeConfig.textSecondary}>{t('video_tutorial')}</span>
        </button>
        
        {/* Telegram Button */}
        <button
          onClick={() => window.open('tg://resolve?domain=audio_code_gen_chhlatbot', '_blank')}
          className={`flex items-center justify-center space-x-2 px-3 py-2 ${themeConfig.card} border-2 ${themeConfig.border} rounded-lg ${themeConfig.borderHover} transition-all duration-200 hover:scale-105 text-sm flex-1 sm:flex-none sm:w-auto`}
        >
          <FaTelegram className="w-4 h-4 text-blue-500" />
          <span className={themeConfig.textSecondary}>{t('send_audio')}</span>
        </button>
      </div>
      
      {/* Language Message Below Buttons */}
      <p className={`${themeConfig.textSecondary} text-sm font-body text-center`}>
        {message} {t('language_guide_message')} <span className="font-semibold text-jade-purple">{clientLang}</span>.
      </p>
    </div>
  )
}