'use client'

import { useLanguage } from '@/context/LanguageContext'
import { useTheme, useThemeConfig } from '@/context/ThemeContext'

export interface PhotoData {
  id: number
  photo_id: string
  photo_url: string[] | null | string
  photo_file_path?: string[] | null
  full_photo_urls?: string[] | null
}

interface SelectedPhotoDisplayProps {
  selectedPhoto: PhotoData | null
  isLoading: boolean
  onRemovePhoto: () => void
  onViewImage: (urls: string[] | null) => void
  disabled?: boolean
  className?: string
}

export default function SelectedPhotoDisplay({
  selectedPhoto,
  isLoading,
  onRemovePhoto,
  onViewImage,
  disabled = false,
  className = "mb-4"
}: SelectedPhotoDisplayProps) {
  const { t } = useLanguage()
  const { theme } = useTheme()
  const themeConfig = useThemeConfig()

  // Handle image click to view gallery
  const handleImageClick = () => {
    if (selectedPhoto) {
      const imageUrls = selectedPhoto.full_photo_urls || 
                       (Array.isArray(selectedPhoto.photo_url) ? selectedPhoto.photo_url : 
                        selectedPhoto.photo_url ? [selectedPhoto.photo_url] : null)
      onViewImage(imageUrls)
    }
  }

  // Get display URL for thumbnail
  const getDisplayUrl = () => {
    if (!selectedPhoto) return null
    
    if (Array.isArray(selectedPhoto.photo_url)) {
      return selectedPhoto.photo_url[0] || null
    }
    return selectedPhoto.photo_url || null
  }

  // Loading state
  if (isLoading) {
    return (
      <div className={`${className} p-4 rounded-xl flex items-center justify-center h-16 border ${themeConfig.card} ${themeConfig.border}`}>
        <div className="flex items-center space-x-3">
          <div className="w-5 h-5 border-2 border-jade-purple border-t-transparent rounded-full animate-spin"></div>
          <span className={`${themeConfig.textSecondary} text-sm`}>{t('loading')}</span>
        </div>
      </div>
    )
  }

  // No photo selected
  if (!selectedPhoto) {
    return null
  }

  const displayUrl = getDisplayUrl()

  return (
    <div className={`${className} p-3 rounded-xl flex items-center justify-between animate-fadeIn border transition-colors ${themeConfig.border} ${themeConfig.borderHover}`}>
      <div className="flex items-center gap-3">
        {/* Photo Thumbnail */}
        <div className={`w-10 h-10 rounded overflow-hidden flex-shrink-0 border ${themeConfig.skeletonElement} ${themeConfig.border}`}>
          {displayUrl ? (
            <img
              src={displayUrl}
              alt={selectedPhoto.photo_id}
              className="w-full h-full object-cover cursor-pointer"
              onClick={handleImageClick}
            />
          ) : (
            <div className={`w-full h-full flex items-center justify-center ${themeConfig.skeletonElement} ${themeConfig.textMuted}`}>
              <span className="text-xs">No Image</span>
            </div>
          )}
        </div>
        {/* Photo ID */}
        <div>
          <p className={`${themeConfig.text}`}>{selectedPhoto.photo_id}</p>
        </div>
      </div>
      {/* Remove Button */}
      <button
        onClick={onRemovePhoto}
        disabled={disabled}
        className={`p-1 rounded-full transition-colors duration-200 border ${disabled ? 'opacity-50 cursor-not-allowed' : ''} ${themeConfig.interactive} hover:bg-red-500/20 ${themeConfig.textSecondary} hover:text-red-500 ${themeConfig.border} hover:border-red-500/30`}
      >
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>
  )
}