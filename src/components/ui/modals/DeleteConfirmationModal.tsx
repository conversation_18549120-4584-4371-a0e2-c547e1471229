'use client'

import { useRef } from 'react'
import { useLanguage } from '@/context/LanguageContext'
import { useTheme, useThemeConfig } from '@/context/ThemeContext'
import { Button } from '@/components/ui'

interface DeleteConfirmationModalProps {
  deleteConfirm: { id: number } | null
  isDeleting: boolean
  onCancel: () => void
  onConfirmDelete: () => void
}

export default function DeleteConfirmationModal({ 
  deleteConfirm, 
  isDeleting, 
  onCancel, 
  onConfirmDelete 
}: DeleteConfirmationModalProps) {
  const { t } = useLanguage()
  const { theme } = useTheme()
  const themeConfig = useThemeConfig()
  const deleteModalRef = useRef<HTMLDivElement>(null)

  if (!deleteConfirm) return null

  return (
    <div
      className={`fixed inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center z-50`}
    >
      <div
        ref={deleteModalRef}
        className={`relative rounded-2xl p-6 w-full max-w-md mx-4 border-2 overflow-hidden ${theme === 'light' ? 'bg-white border-gray-300' : 'bg-red-900/[0.8] border-red-500/50'}`}
        onClick={(e: React.MouseEvent) => e.stopPropagation()}
      >
        {theme === 'dark' && (
          <div className="absolute inset-0 bg-gradient-to-br from-red-500/10 to-transparent opacity-50 rounded-2xl"></div>
        )}
        <div className="relative z-10">
          <h3 className={`text-xl font-bold mb-4 font-title text-center ${themeConfig.text}`}>
            {t('delete_item')}
          </h3>
          <p className={`${themeConfig.textSecondary} mb-6 text-center`}>
            {t('kb_delete_confirmation')}
          </p>
          <div className="flex justify-between w-full space-x-4">
            <Button
              onClick={onCancel}
              variant="cancel"
              size="md"
              className="flex-1"
              disabled={isDeleting}
            >
              {t('cancel')}
            </Button>
            <Button
              onClick={onConfirmDelete}
              variant="danger"
              size="md"
              className="flex-1"
              disabled={isDeleting}
            >
              {isDeleting ? (
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              ) : (
                t('delete')
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}