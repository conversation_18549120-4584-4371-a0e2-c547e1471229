'use client'

import { useRef, useEffect } from 'react'
import { useLanguage } from '@/context/LanguageContext'
import { useTheme, useThemeConfig } from '@/context/ThemeContext'

interface EditModalProps {
  editingItem: {
    id: number
    field: 'question' | 'answer'
    value: string
  } | null
  hasFocusedInput: boolean
  onValueChange: (value: string) => void
  onInputFocus: () => void
  onSave: () => void
  onClose: () => void
}

export default function EditModal({ 
  editingItem, 
  hasFocusedInput, 
  onValueChange, 
  onInputFocus, 
  onSave, 
  onClose 
}: EditModalProps) {
  const { t } = useLanguage()
  const { theme } = useTheme()
  const themeConfig = useThemeConfig()
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const modalRef = useRef<HTMLDivElement>(null)

  if (!editingItem) return null

  return (
    <div
      className="fixed inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center z-50"
    >
      <div
        ref={modalRef}
        className={`relative ${themeConfig.card} rounded-2xl p-6 w-full max-w-lg mx-4 border-2 ${themeConfig.border} overflow-hidden shadow-xl`}
        onClick={(e: React.MouseEvent) => e.stopPropagation()}
      >
        <div className="relative z-10" data-modal-content>
          {/* Close button (X) */}
          <button
            className={`absolute top-0 right-0 p-1 rounded-full ${themeConfig.interactiveHover} ${themeConfig.textSecondary} ${themeConfig.border} border transition-colors`}
            onClick={onClose}
            aria-label="Close"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>

          <h3 className={`text-xl ${themeConfig.text} font-bold mb-4 font-title text-center`}>
            {editingItem.field === 'question' ? t('kb_edit_question') : t('edit_reply')}
          </h3>
          <textarea
            ref={textareaRef}
            value={editingItem.value}
            onChange={(e) => {
              // Only update if value actually changed
              if (e.target.value !== editingItem.value) {
                onValueChange(e.target.value);
              }
            }}
            onClick={() => {
              // On mobile: only position cursor at end on FIRST click (initial focus)
              // After that, allow free cursor movement
              if (!hasFocusedInput && textareaRef.current) {
                const length = textareaRef.current.value.length;
                textareaRef.current.setSelectionRange(length, length);
                onInputFocus();
              }
              // Subsequent clicks: let user position cursor freely (default browser behavior)
            }}
            className={`w-full px-4 py-2 ${themeConfig.secondCard} border ${themeConfig.border} rounded-lg ${themeConfig.text} placeholder-gray-400 focus:outline-none min-h-[150px] mb-4 ${themeConfig.borderHover}`}
            placeholder={editingItem.field === 'question' ? t('enter_question') : t('enter_reply')}
          />
          <button
            onClick={onSave}
            className="bg-jade-purple-dark text-white hover:bg-jade-purple hover:shadow-md hover:bg-jade-purple transition-all duration-200 px-6 py-2 rounded-lg font-medium w-full border border-jade-purple/75"
          >
            {t('done')}
          </button>
        </div>
      </div>
    </div>
  )
}