'use client'

import { useRef } from 'react'
import { useLanguage } from '@/context/LanguageContext'
import { useTheme, useThemeConfig } from '@/context/ThemeContext'

interface ViewModalProps {
  viewingItem: {
    field: 'question' | 'answer'
    value: string
  } | null
  questions: Array<{
    question: string
    answer: string
  }>
  onClose: () => void
}

export default function ViewModal({ viewingItem, questions, onClose }: ViewModalProps) {
  const { t } = useLanguage()
  const { theme } = useTheme()
  const themeConfig = useThemeConfig()
  const viewModalRef = useRef<HTMLDivElement>(null)

  if (!viewingItem) return null

  return (
    <div
      className="fixed inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center z-50"
    >
      <div
        ref={viewModalRef}
        className={`relative rounded-2xl p-6 w-full max-w-lg mx-4 border-2 overflow-hidden ${themeConfig.card} ${themeConfig.border}`}
        onClick={(e: React.MouseEvent) => e.stopPropagation()}
      >
        <div className="relative z-10">
          {/* Close button (X) in the top-right corner */}
          <button
            className={`absolute top-0 right-0 p-1 rounded-full ${themeConfig.interactive} ${themeConfig.interactiveHover} ${themeConfig.textSecondary} ${themeConfig.border} border transition-colors`}
            onClick={onClose}
            aria-label="Close"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>

          <h3 className={`text-xl font-bold mb-4 font-title text-center ${themeConfig.text}`}>
            {viewingItem.field === 'question' ? t('question') : t('reply')} 
            <span className={`${themeConfig.textSecondary} ml-1`}>
              #{questions.findIndex(q => 
                viewingItem.field === 'question' ? q.question === viewingItem.value : q.answer === viewingItem.value
              ) + 1}
            </span>
          </h3>
          <div className={`${themeConfig.secondCard} border-2 ${themeConfig.border} rounded-lg p-4 ${themeConfig.text} mb-4 min-h-[150px] max-h-[200px] overflow-y-auto whitespace-pre-wrap`}>
            {viewingItem.value}
          </div>
        </div>
      </div>
    </div>
  )
}