-- Enable vector extension
CREATE EXTENSION IF NOT EXISTS vector;

-- Create FAQs table
CREATE TABLE public.faqs (
  id SERIAL NOT NULL,
  client_id text NOT NULL,
  question TEXT NULL,
  question_p TEXT NULL,
  answer TEXT NULL,
  answer_p TEXT NULL,
  audio_url TEXT NULL,
  photo_url text[] NULL,
  photo_id TEXT NULL,
  audio_duration INTEGER NULL,
  audio_file_path TEXT NULL,
  updated_at TIMESTAMP WITH TIME ZONE NULL DEFAULT now(),
  created_at TIMESTAMP WITH TIME ZONE NULL DEFAULT now(),
  faq_id TEXT NULL,
  fb_photo_atmid text[] NULL,
  fb_audio_atmid TEXT NULL,
  tg_photo_atmid text[] NULL,
  tg_audio_atmid TEXT NULL,
  ig_photo_atmid text[] NULL,
  ig_audio_atmid TEXT NULL,
  is_visible BOOLEAN NULL,
  embedding VECTOR(512) NULL,
  
  CONSTRAINT faqs_pkey PRIMARY KEY (id),
  CONSTRAINT faqs_client_id_fkey FOREIGN KEY (client_id) REFERENCES clients (client_id)
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_faqs_client_id ON public.faqs (client_id);
CREATE INDEX IF NOT EXISTS idx_faqs_client_visible ON public.faqs (client_id, is_visible) WHERE is_visible = true;
-- Vector similarity search index (ESSENTIAL for performance)
CREATE INDEX IF NOT EXISTS idx_faqs_embedding ON public.faqs USING hnsw (embedding vector_cosine_ops);

-- Add trigger for auto-updating updated_at
CREATE TRIGGER trg_update_faqs_timestamp 
  BEFORE UPDATE ON public.faqs 
  FOR EACH ROW 
  EXECUTE FUNCTION public.update_modified_column();


DROP FUNCTION IF EXISTS public.fetch_similar_faqs(vector(512), text, integer);  

-- =====================================================
-- OPTIMIZED RAG SEARCH FUNCTION
-- =====================================================
CREATE OR REPLACE FUNCTION public.fetch_similar_faqs(
  query_embedding vector(512),
  p_client_id text,
  match_count INT DEFAULT 15
)
RETURNS TABLE (
  id INT,
  question TEXT,
  question_p TEXT,
  answer TEXT,
  answer_p TEXT,
  audio_url TEXT,
  photo_url text[],
  audio_duration INT,
  fb_photo_atmid text[],
  fb_audio_atmid TEXT,
  tg_photo_atmid text[],
  tg_audio_atmid TEXT,
  ig_photo_atmid text[],
  ig_audio_atmid TEXT,
  faq_id TEXT,
  similarity FLOAT
)
LANGUAGE plpgsql
STABLE
PARALLEL SAFE
AS $$
BEGIN
  RETURN QUERY
    SELECT
      f.id,
      f.question,
      f.question_p,
      f.answer,
      f.answer_p,
      f.audio_url,
      f.photo_url,
      f.audio_duration,
      f.fb_photo_atmid,
      f.fb_audio_atmid,
      f.tg_photo_atmid,
      f.tg_audio_atmid,
      f.ig_photo_atmid,
      f.ig_audio_atmid,
      f.faq_id,
      (1 - (f.embedding <=> query_embedding))::FLOAT AS similarity
    FROM public.faqs f
    WHERE f.client_id = p_client_id
      AND f.embedding IS NOT NULL
    ORDER BY f.embedding <=> query_embedding
    LIMIT match_count;
END;
$$;

-- =====================================================
-- PERFORMANCE MONITORING VIEW
-- =====================================================
CREATE OR REPLACE VIEW public.faq_embedding_stats AS
SELECT 
  client_id,
  COUNT(*) as total_faqs,
  COUNT(embedding) as faqs_with_embedding,
  COUNT(CASE WHEN is_visible THEN 1 END) as visible_faqs,
  COUNT(CASE WHEN NOT is_visible THEN 1 END) as hidden_variants,
  ROUND(COUNT(embedding)::NUMERIC / COUNT(*) * 100, 2) as embedding_coverage_pct
FROM public.faqs
GROUP BY client_id;

-- =====================================================
-- EXAMPLE USAGE
-- =====================================================

/*
-- Get top 15 similar FAQs (for app-side deduplication):
SELECT * FROM public.fetch_similar_faqs(
  '[0.1,0.2,0.3,...]'::vector(512), 
  'client-123', 
  15
);

-- Get top 25 for more safety margin:
SELECT * FROM public.fetch_similar_faqs(
  '[0.1,0.2,0.3,...]'::vector(512), 
  'client-123', 
  25
);

-- Monitor embedding coverage:
SELECT * FROM public.faq_embedding_stats WHERE client_id = 'client-123';
*/

  

-- Example usage:
-- SELECT * FROM fetch_similar_faqs('[0.1,0.2,0.3,...]'::vector(512), 'your-uuid-here'::uuid, 5);



-- # Fast app-side deduplication
-- def get_distinct_faqs(embedding, client_id, wanted=3):
--     results = fetch_similar_faqs(embedding, client_id, 15)
--     seen = set()
--     distinct = []
--     for faq in results:
--         if faq.faq_id not in seen:
--             seen.add(faq.faq_id)
--             distinct.append(faq)
--             if len(distinct) == wanted:
--                 break
--     return distinct