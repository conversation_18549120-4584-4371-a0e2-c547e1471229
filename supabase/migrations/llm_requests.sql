CREATE TABLE public.llm_requests (
  id SERIAL PRIMARY KEY,
  conversation_id TEXT,
  client_id TEXT NOT NULL,
  customer_id TEXT NOT NULL,
  platform TEXT,
  operation TEXT,
  task TEXT,
  model_code TEXT,
  model TEXT,
  provider TEXT,
  input_tokens INTEGER DEFAULT 0,
  output_tokens INTEGER DEFAULT 0,
  cache_read_tokens INTEGER DEFAULT 0,
  cache_write_tokens INTEGER DEFAULT 0,
  thinking_tokens INTEGER DEFAULT 0,
  audio_input_tokens INTEGER DEFAULT 0,
  input_cost DECIMAL(12,9) DEFAULT 0,
  output_cost DECIMAL(12,9) DEFAULT 0,
  cache_read_cost DECIMAL(12,9) DEFAULT 0,
  cache_write_cost DECIMAL(12,9) DEFAULT 0,
  thinking_cost DECIMAL(12,9) DEFAULT 0,
  audio_input_cost DECIMAL(12,9) DEFAULT 0,
  total_cost DECIMAL(12,9) NOT NULL,
  flag TEXT,
  created_at TIMESTAMP WITH TIME ZONE NULL DEFAULT NOW()
);

-- Essential indexes for analytics
CREATE INDEX IF NOT EXISTS idx_llm_requests_client_id ON public.llm_requests (client_id);
CREATE INDEX IF NOT EXISTS idx_llm_requests_conversation_id ON public.llm_requests (conversation_id);
CREATE INDEX IF NOT EXISTS idx_llm_requests_created_at ON public.llm_requests (created_at DESC);
CREATE INDEX IF NOT EXISTS idx_llm_requests_client_created ON public.llm_requests (client_id, created_at DESC);
