CREATE TABLE public.photos (
  id SERIAL NOT NULL,
  photo_id TEXT NULL,
  client_id text NOT NULL,
  photo_url text[] NULL,
  photo_file_path text[] NULL,
  created_at TIMESTAMP WITH TIME ZONE NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NULL DEFAULT now(),
  
  CONSTRAINT photos_pkey PRIMARY KEY (id),
  CONSTRAINT photos_client_id_fkey FOREIGN KEY (client_id) REFERENCES clients (client_id)
);

-- Essential indexes
CREATE INDEX IF NOT EXISTS idx_photos_client_id ON public.photos (client_id);

-- Add trigger for auto-updating updated_at
CREATE TRIGGER trg_update_photos_timestamp 
  BEFORE UPDATE ON public.photos 
  FOR EACH ROW 
  EXECUTE FUNCTION public.update_modified_column();