CREATE TABLE public.welcome_chat (
  id SERIAL NOT NULL,
  client_id text NOT NULL,
  answer TEXT NULL,
  answer_p TEXT NULL,
  audio_url TEXT NULL,
  photo_url text[] NULL,
  photo_id TEXT NULL,
  audio_duration INTEGER NULL,
  audio_file_path TEXT NULL,
  updated_at TIMESTAMP WITH TIME ZONE NULL DEFAULT now(),
  created_at TIMESTAMP WITH TIME ZONE NULL DEFAULT now(),
  chat_id TEXT NULL,
  fb_photo_atmid text[] NULL,
  fb_audio_atmid TEXT NULL,
  tg_photo_atmid text[] NULL,
  tg_audio_atmid TEXT NULL,
  ig_photo_atmid text[] NULL,
  ig_audio_atmid TEXT NULL,
  
  CONSTRAINT welcome_chat_pkey PRIMARY KEY (id),
  CONSTRAINT welcome_chat_client_id_fkey FOREIGN KEY (client_id) REFERENCES clients (client_id)
);

-- Essential indexes
CREATE INDEX IF NOT EXISTS idx_welcome_chat_client_id ON public.welcome_chat (client_id);

-- Add trigger for auto-updating updated_at
CREATE TRIGGER trg_update_welcome_chat_timestamp 
  BEFORE UPDATE ON public.welcome_chat 
  FOR EACH ROW 
  EXECUTE FUNCTION public.update_modified_column();