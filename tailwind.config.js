/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
    './src/context/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  safelist: [
    'text-zinc-900',
    'text-zinc-800', 
    'text-zinc-700',
    'text-zinc-600',
    'text-zinc-500',
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ['var(--font-nunito)', 'Nunito', 'var(--font-zilla-slab)', 'Zilla Slab', 'sans-serif'],
        title: ['var(--font-raleway)', 'Raleway', 'var(--font-caladea)', 'Caladea', 'sans-serif'],
      },
      colors: {
        // Brand colors
        'jade-black': '#000000',
        'jade-white': '#FFFFFF',
        'jade-purple': '#6135E6',
        'jade-purple-dark': '#532CC7',
        'jade-purple-light': '#E1E0FF',
        'jade-gray': '#CCCCCC',
        'deep-blue': '#03011f',
        'dark-brown': '#2d1810',
        
        // Theme colors - Dark (from provided HSL values)
        // 'dark': {
        //   bg: '#03011f',
        //   bgDarker: '#03011f',
        //   bgLighter: '#482187',
        // },
        
        // // Theme colors - Light (from provided HSL values)
        // 'light': {
        //   // bg: 'hsl(243 100% 98%)',
        //   bg: '#F5F7FB',
        //   bgDarker: 'hsl(249 25% 91%)',
        //   bgLighter: '#b49ed9',
        // },
        // New structured dashboard theme colors
        'dashboard': {
          // Dark theme
          'dark-bg': '#01001c',
          'dark-surface': '#0F0F2A',
          'dark-surface-dark': '#18183A',
          'dark-text-primary': '#E5E7EB',
          'dark-text-secondary': '#E1E2E6',
          'dark-text-muted': '#D1D5DB',
          'dark-text-disabled': '#6B7280',
          'dark-text-inverse': '#1A1A1A',
          'dark-border-default': '#333366',
          'dark-border-hover': '#7672E8',
          'dark-border-active': '#6B7280',
          'dark-border-muted': '#2A2A2A',
          'dark-interactive': '#18183A',
          'dark-interactive-hover': '#0F0F2A',
          'dark-interactive-pressed': '#525252',
          'dark-interactive-disabled': '#2A2A2A',
          
          // Light theme
          'light-bg': '#F5F7FB',
          'light-surface': '#FFFFFF',
          'light-surface-dark': '#FAFAFA',
          'light-text-primary': '#4B5563',
          'light-text-secondary': '#4B5563',
          'light-text-muted': '#374151',
          'light-text-disabled': '#D1D5DB',
          'light-text-inverse': '#FFFFFF',
          'light-border-default': '#D3DCE5',
          'light-border-hover': '#B8C5D1',
          'light-border-active': '#9CA3AF',
          'light-border-muted': '#F3F4F6',
          'light-interactive': '#F6F6F6',
          'light-interactive-hover': '#F5F7FB',
          'light-interactive-pressed': '#F3F4F6',
          'light-interactive-disabled': '#F9FAFB',
          
          // Shared colors
          'primary': '#532CC7',
          'success': '#22C55E',
          'success-light': '#16A34A',
          'error': '#EF4444',
          'error-light': '#DC2626',
          'warning': '#F59E0B',
          'warning-light': '#D97706',
          'info': '#3B82F6',
          'info-light': '#2563EB',
        },
      },
      backgroundImage: {
        'gradient-purple': 'linear-gradient(to right, #866bff, #7559e8)',
      },
      animation: {
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'fadeIn': 'fadeIn 0.5s ease-in-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        }
      }
    },
  },
  plugins: [],
}